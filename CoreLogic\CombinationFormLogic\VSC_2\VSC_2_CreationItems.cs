﻿using Autodesk.Revit.DB;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MEP.SheetCreator.CoreLogic.CombinationFormLogic.VSC_2
{
    public class VSC_2_CreationItems
    {
        #region Fields
        public string SheetName;
        public string SheetNumber;
        public ElementId TitleBlockId;

        public Autodesk.Revit.DB.View View;
        public string ViewName;
        public string ViewTemplateName;
        public string SecondaryViewTemplateName;

        public ElementId LevelId;
        public ViewFamilyType ViewFamilyType;

        public bool IsDependentView;
        public string DependentViewName;
        public ViewPlan ParentView;
        public Element ScopeBox;

        public int? Scale;

        public string DisciplineName;
        public string OverallParentDependent;
        public string BaseViewName;

        public string Designer;
        public string DrawnBy;
        public string VerifiedBy;
        public string Date;

        #endregion

        #region Properties



        #endregion

        #region Constructors
        public VSC_2_CreationItems(Document doc, bool isDependent, DataGridViewRow row, ElementId titleBlockId, Level level, Element scopebox)
        {
            SheetName = row.Cells[(int)AdvancedItemsColumn.SheetName].Value.ToString();
            SheetNumber = row.Cells[(int)AdvancedItemsColumn.SheetNumber].Value.ToString();
            TitleBlockId = titleBlockId;

            if (isDependent)
            {
                IsDependentView = isDependent;
                DependentViewName = row.Cells[(int)AdvancedItemsColumn.ViewName].Value.ToString();
                if (scopebox != null)
                    ScopeBox = scopebox;
            }
            else
            {
                IsDependentView = false;
                ViewName = row.Cells[(int)AdvancedItemsColumn.ViewName].Value.ToString();
                ViewTemplateName = row.Cells[(int)AdvancedItemsColumn.PrimaryViewTemplate].Value.ToString();
                SecondaryViewTemplateName = row.Cells[(int)AdvancedItemsColumn.SecondaryViewTemplate].Value.ToString();
                if (scopebox != null)
                    ScopeBox = scopebox;
            }

            LevelId = level.Id;
            ViewFamilyType = GetViewFamilyType(doc, row.Cells[(int)AdvancedItemsColumn.ViewType].Value.ToString());

            var sc = row.Cells[(int)AdvancedItemsColumn.Scale].Value.ToString();
            if (sc != "")
                Scale = Convert.ToInt32(sc.Remove(0, 2));

            DisciplineName = row.Cells[(int)AdvancedItemsColumn.DisciplineName].Value.ToString();
            OverallParentDependent = row.Cells[(int)AdvancedItemsColumn.View].Value.ToString();
            BaseViewName = row.Cells[(int)AdvancedItemsColumn.BaseViewName].Value.ToString();

            Designer = row.Cells[(int)AdvancedItemsColumn.Designer].Value.ToString();
            DrawnBy = row.Cells[(int)AdvancedItemsColumn.DrawnBy].Value.ToString();
            VerifiedBy = row.Cells[(int)AdvancedItemsColumn.VerifiedBy].Value.ToString();
            Date = row.Cells[(int)AdvancedItemsColumn.Date].Value.ToString();
        }


        #endregion

        #region Methods

        private ViewFamilyType GetViewFamilyType(Document doc, string viewTypeName)
        {
            return new FilteredElementCollector(doc).OfClass(typeof(ViewFamilyType))
                .Cast<ViewFamilyType>().FirstOrDefault<ViewFamilyType>(x => GetViewFamily(viewTypeName) == x.ViewFamily);
        }

        private ViewFamily GetViewFamily(string viewTypeName)
        {
            switch (viewTypeName)
            {
                case "FloorPlan":
                    return ViewFamily.FloorPlan;
                case "ThreeD":
                    return ViewFamily.ThreeDimensional;
                case "Elevation":
                    return ViewFamily.Elevation;
                case "Section":
                    return ViewFamily.Section;
                case "CeilingPlan":
                    return ViewFamily.CeilingPlan;
                case "EngineeringPlan":
                    return ViewFamily.StructuralPlan;
                case "DraftingView":
                    return ViewFamily.Drafting;
                default:
                    return ViewFamily.FloorPlan;
            }
        }

        #endregion

    }
}
