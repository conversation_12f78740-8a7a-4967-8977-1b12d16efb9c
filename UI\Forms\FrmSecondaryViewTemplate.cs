﻿using MEP.SheetCreator.CoreLogic;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MEP.SheetCreator.UI.Forms
{
    public partial class FrmSecondaryViewTemplate : Form
    {
        public string SelectedSecondViewTemplate;

        VSC_Data _data;
        IEnumerable<VSC_ViewTemplate> _viewTypeToshow;

        public FrmSecondaryViewTemplate(VSC_Data data, string viewType)
        {
            InitializeComponent();
            _data = data;

            cb_ViewTypes.DataSource = data.ViewTemplates.Select(x => x.View.ViewType.ToString()).Distinct().ToList();
            cb_ViewTypes.SelectedIndex = cb_ViewTypes.Items.IndexOf(viewType);

            _viewTypeToshow = data.ViewTemplates.Where(x => x.ViewType == viewType);

            PopulateList(_data);

            
        }

        private void PopulateList(VSC_Data data)
        {
            _viewTypeToshow = data.ViewTemplates.Where(x => x.ViewType == cb_ViewTypes.SelectedItem.ToString());
            foreach (var item in _viewTypeToshow)
            {
                lb_SecondaryViewTemplates.Items.Add(item.Name);

            }
        }

        private void btn_01_Assign2ndVT_Click(object sender, EventArgs e)
        {
            SelectedSecondViewTemplate = lb_SecondaryViewTemplates.SelectedItem.ToString();
        }

        private void cb_ViewTypes_SelectedIndexChanged(object sender, EventArgs e)
        {
            lb_SecondaryViewTemplates.Items.Clear();
            PopulateList(_data);
        }
    }
}
