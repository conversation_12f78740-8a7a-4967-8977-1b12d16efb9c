﻿using Autodesk.Revit.DB;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.SheetCreator.CoreLogic
{
    public class SC_Sheet
    {
        #region Fields
        public FamilySymbol TitleBlock;
        public string TitleBlockName { get; set; }


        #endregion

        #region Propertises



        #endregion

        #region Constructors
        public SC_Sheet(FamilySymbol titleBlock)
        {
            TitleBlock = titleBlock;
            TitleBlockName = titleBlock.Name;
        }


        #endregion

        #region Methods



        #endregion

    }
}