﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using BecaTransactionsNamesManager;
using Common.UI.Forms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TaskDialog = Autodesk.Revit.UI.TaskDialog;
using View = Autodesk.Revit.DB.View;

namespace MEP.SheetCreator.CoreLogic.CombinationFormLogic.Batch2DView
{
    public static class Batch2DViewLogic
    {

        public static bool RunLogic(Document doc, IList<Level> subLevelsSel, IList<Element> subFloorTempsSel, IList<Element> subRcpTempsSel, ElementId subScopeBoxSelId, ViewFamilyType SelectedViewFamily, string subStrViewPrefix, string subStrViewSuffix,  out int intViewCreated, out List<ViewPlan> createdView)
        {
            try
            {
                intViewCreated = 0;
                createdView = new List<ViewPlan>();

                //Get Floor view type and ceiling view type
                ViewFamilyType rqVtfRCP = new FilteredElementCollector(doc).OfClass(typeof(ViewFamilyType))
                .Cast<ViewFamilyType>().FirstOrDefault<ViewFamilyType>(x => ViewFamily.CeilingPlan == x.ViewFamily);

                //Get all plan views (for checking view names to be unique)
                FilteredElementCollector rqColViewPlans = new FilteredElementCollector(doc).OfClass(typeof(ViewPlan));
                IList<Element> rqViewPlans = rqColViewPlans.ToElements();

                //Start Creating Views by Level, View Type, View Template, and Scope Box
                int nCount = (subLevelsSel.Count * subFloorTempsSel.Count) + (subLevelsSel.Count * subRcpTempsSel.Count);
                string progressMessage = "{0} of " + nCount.ToString() + " views processed...";
                string caption = "Creating Views";
                using (BecaProgressForm pf = new BecaProgressForm(caption, progressMessage, nCount))
                {
                    try
                    {
                        using (var trans = new Transaction(doc, BecaTransactionsNames.batch2DView_CreateViews.GetHumanReadableString()))
                        {
                            trans.Start();
                            foreach (Level rqLevel in subLevelsSel)
                            {
                                foreach (View rqViewTemp in subFloorTempsSel)
                                {
                                    intViewCreated = CreateAview(doc, rqViewPlans, SelectedViewFamily, rqLevel, rqViewTemp.Name, rqViewTemp.Id, subScopeBoxSelId, intViewCreated,
                                           subStrViewPrefix, subStrViewSuffix, createdView);
                                    pf.Increment();
                                }

                                foreach (View rqViewTemp in subRcpTempsSel)
                                {
                                    intViewCreated = CreateAview(doc, rqViewPlans, rqVtfRCP, rqLevel, rqViewTemp.Name, rqViewTemp.Id, subScopeBoxSelId, intViewCreated,
                                           subStrViewPrefix, subStrViewSuffix, createdView);
                                    pf.Increment();
                                }
                            }
                            trans.Commit();
                        }
                        
                        pf.Close();
                    }
                    catch (Exception ex)
                    {
                        TaskDialog.Show("Exception", ex.Message);
                        return false;
                    }

                }

                return true;
            }
            catch (Exception)
            {
                intViewCreated = 0;
                createdView = null;
                return false;
            }
            
        }

        private static int CreateAview(Document rqSubDoc, IList<Element> rqSubViews, ViewFamilyType rqSubVFtype, Level subLevel, string subStrViewName, ElementId rqSubViewTMPid, ElementId rqSubSBXid, int intViewCreated,
            string strFnViewPrefix, string strFnViewSuffix, List<ViewPlan> createdViews)
        {
            // Process and make ViewCreate name
            if (subStrViewName.Substring(0, 4).ToUpper() == "BECA")
            {
                subStrViewName = subStrViewName.Substring(8);
            }
            subStrViewName = strFnViewPrefix + subLevel.Name + " " + subStrViewName + strFnViewSuffix;

            // Test if find the same view name existed, then do not create the view
            foreach (ViewPlan rqVp in rqSubViews)
            {
                if (!(rqVp.IsTemplate) && rqVp.GenLevel.Id == subLevel.Id && rqVp.Name.ToUpper() == subStrViewName.ToUpper())
                {
                    return intViewCreated = 0;
                }
            }

            // The view name did not exist, so start creating the new view
            ViewPlan rqViewCreate = ViewPlan.Create(rqSubDoc, rqSubVFtype.Id, subLevel.Id);
            rqViewCreate.Name = subStrViewName;
            rqViewCreate.ViewTemplateId = rqSubViewTMPid;
            if (null != rqSubSBXid)
            {
                rqViewCreate.get_Parameter(BuiltInParameter.VIEWER_VOLUME_OF_INTEREST_CROP).Set(rqSubSBXid);
            }
            createdViews.Add(rqViewCreate);
            intViewCreated++;
            return intViewCreated;

        }
    }
}
