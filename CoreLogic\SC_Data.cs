﻿using Autodesk.Revit.DB;
using BecaRevitUtilities.Collectors;
using BecaRevitUtilities.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using View = Autodesk.Revit.DB.View;


namespace MEP.SheetCreator.CoreLogic
{
    public class SC_Data
    {

        #region Fields
        public List<SC_Sheet> Sheets;

        public List<FamilySymbol> TitleBlocks;
        public List<SC_ParentView> ParentViews;
        public List<Level> Levels;
        public List<SC_ViewTemplate> ViewTemplate;
        public List<Element> ScopeBoxes;

        public IList<View> AvailableViews = new List<View>();
        public IList<View> AvailableLegends = new List<View>();
        public IList<ViewSheet> AvailableSheets = new List<ViewSheet>();

        public List<SC_Discipline> Disciplines;

        #endregion

        #region Constructors
        public SC_Data(Document doc)
        {
            Sheets = AssignTitleBlocks(doc);

            TitleBlocks = GetTitleBlocks(doc);
            GetParentViews(doc);
            Levels = GetLevels(doc);
            GetViewTemplates(doc);
            ScopeBoxes = GetScopeBoxes(doc);

            Disciplines = new List<SC_Discipline>();
            AssignDisciplines();

            GetViewsSheetsLegends(doc);
        }


        #endregion

        #region Methods
        private List<SC_Sheet> AssignTitleBlocks(Document doc)
        {
            var sC_TitleBlocks = new List<SC_Sheet>();
            var titleBlocks = ElementCollectorUtility.GetAllTitleBlocks(doc).Select(x => x as FamilySymbol).ToList();
            titleBlocks.ForEach(x => sC_TitleBlocks.Add(new SC_Sheet(x)));
            return sC_TitleBlocks;
        }

        private List<FamilySymbol> GetTitleBlocks(Document doc)
        {
            return ElementCollectorUtility.GetAllTitleBlocks(doc).Select(x => x as FamilySymbol).ToList();
        }

        private void GetParentViews(Document doc)
        {
            var views = new List<ViewPlan>(new FilteredElementCollector(doc).OfClass(typeof(ViewPlan)).Cast<ViewPlan>()
               .Where<ViewPlan>(v => !v.IsTemplate && ((ViewType.FloorPlan == v.ViewType)
               || (ViewType.CeilingPlan == v.ViewType) || (ViewType.EngineeringPlan == v.ViewType))));

            var viewsSortByName = from ViewPlan view in views orderby view.ViewType, view.Name ascending select view;

            ParentViews = new List<SC_ParentView>();
            foreach (ViewPlan item in viewsSortByName)
            {
                if (item.GetPrimaryViewId().IntegerValue() == -1)
                {
                    ParentViews.Add(new SC_ParentView(item));
                }
            }
        }

        private List<Level> GetLevels(Document doc)
        {
            var levelElements = new FilteredElementCollector(doc).OfClass(typeof(Level)).ToElements();
            var sortLevelElements = from Level rqLv in levelElements orderby rqLv.Elevation ascending select rqLv;
            var levels = new List<Level>();
            foreach (Level item in sortLevelElements)
            {
                levels.Add(item);
            }
            return levels;
        }

        private void GetViewTemplates(Document doc)
        {
            var viewTemplates = new FilteredElementCollector(doc).OfClass(typeof(View)).Cast<View>().Where(x => x.IsTemplate).ToList();
            var sortViewElements = from View v in viewTemplates orderby v.Name ascending select v;

            ViewTemplate = new List<SC_ViewTemplate>();
            foreach (View item in sortViewElements)
            {
                ViewTemplate.Add(new SC_ViewTemplate(item));
            }
        }

        private List<Element> GetScopeBoxes(Document doc)
        {
            return new FilteredElementCollector(doc).OfCategory(BuiltInCategory.OST_VolumeOfInterest).ToElements().ToList();
        }

        private void GetViewsSheetsLegends(Document doc)
        {
            var views = new FilteredElementCollector(doc).OfClass(typeof(View)).Cast<View>()
                .Where(x => !x.Name.Contains("<Revision Schedule>")).ToList();
            var viewports = new FilteredElementCollector(doc).OfClass(typeof(Viewport)).Cast<Viewport>().ToList();

            foreach (View item in views)
            {
                if (null != item && (!item.IsTemplate))
                {
                    switch (item.ViewType)
                    {
                        //Only these view types would be placed on sheets.
                        case ViewType.ThreeD:
                        case ViewType.FloorPlan:
                        case ViewType.Elevation:
                        case ViewType.Schedule:
                        case ViewType.PanelSchedule:
                        case ViewType.CeilingPlan:
                        case ViewType.DraftingView:
                        case ViewType.Section:
                        case ViewType.EngineeringPlan:
                            bool blViewUsed = false;
                            //going through all placed views
                            foreach (Viewport rqVP in viewports)
                            {
                                //if this view has been placed
                                if (rqVP.ViewId == item.Id)
                                {
                                    //Flag view placed, and remove viewport from collection to avoid checking next time.
                                    blViewUsed = true;
                                    viewports.Remove(rqVP);
                                    break;
                                }
                            }
                            //Only add a document view that has not been placed to the view collection
                            if (!blViewUsed)
                            {
                                AvailableViews.Add(item);
                            }
                            break;
                        case ViewType.Legend:
                            AvailableLegends.Add(item);
                            break;
                        case ViewType.DrawingSheet:
                            ViewSheet rqViewSheet = item as ViewSheet;
                            //Place holder sheets won't be collected
                            if (!rqViewSheet.IsPlaceholder)
                            {
                                AvailableSheets.Add(rqViewSheet);
                            }
                            break;
                        default:
                            break;
                    }
                }
            }
        }

        private void AssignDisciplines()
        {
            Disciplines.Add(new SC_Discipline("BA", "Cover Sheet", 0));
            Disciplines.Add(new SC_Discipline("BA", "Drawing Index", 0));
            Disciplines.Add(new SC_Discipline("BA", "Legend", 0));
            Disciplines.Add(new SC_Discipline("BA", "Schematic", 0));
            Disciplines.Add(new SC_Discipline("BA", "Site Plan", 1));
            Disciplines.Add(new SC_Discipline("BA", "Demolition", 1));
            Disciplines.Add(new SC_Discipline("BA", "Temporary Works", 1));
            Disciplines.Add(new SC_Discipline("BA", "Plan", 2));
            Disciplines.Add(new SC_Discipline("BA", "Sections & 3D Views", 8));
            Disciplines.Add(new SC_Discipline("BA", "General Details", 9));

            Disciplines.Add(new SC_Discipline("BB", "Cover Sheet", 0));
            Disciplines.Add(new SC_Discipline("BB", "Drawing Index", 0));
            Disciplines.Add(new SC_Discipline("BB", "Legend", 0));
            Disciplines.Add(new SC_Discipline("BB", "Schematic", 0));
            Disciplines.Add(new SC_Discipline("BB", "Site Plan", 1));
            Disciplines.Add(new SC_Discipline("BB", "Demolition", 1));
            Disciplines.Add(new SC_Discipline("BB", "Temporary Works", 1));
            Disciplines.Add(new SC_Discipline("BB", "Plan", 2));
            Disciplines.Add(new SC_Discipline("BB", "Reflected Ceiling Layout", 2));
            Disciplines.Add(new SC_Discipline("BB", "Combined Services Layout", 3));
            Disciplines.Add(new SC_Discipline("BB", "Combined Services Detail Layout", 7));
            Disciplines.Add(new SC_Discipline("BB", "Sections & 3D Views", 8));
            Disciplines.Add(new SC_Discipline("BB", "General Details", 9));

            Disciplines.Add(new SC_Discipline("BC", "Cover Sheet", 0));
            Disciplines.Add(new SC_Discipline("BC", "Drawing Index", 0));
            Disciplines.Add(new SC_Discipline("BC", "Legend", 0));
            Disciplines.Add(new SC_Discipline("BC", "Schematic", 0));
            Disciplines.Add(new SC_Discipline("BC", "Site Plan", 1));
            Disciplines.Add(new SC_Discipline("BC", "Demolition", 1));
            Disciplines.Add(new SC_Discipline("BC", "Temporary Works", 1));
            Disciplines.Add(new SC_Discipline("BC", "Communication Layout", 3));
            Disciplines.Add(new SC_Discipline("BC", "Comms Detail Layout", 7));
            Disciplines.Add(new SC_Discipline("BC", "Sections & 3D Views", 8));
            Disciplines.Add(new SC_Discipline("BC", "General Details", 9));

            Disciplines.Add(new SC_Discipline("BE", "Cover Sheet", 0));
            Disciplines.Add(new SC_Discipline("BE", "Drawing Index", 0));
            Disciplines.Add(new SC_Discipline("BE", "Legend", 0));
            Disciplines.Add(new SC_Discipline("BE", "Schematic", 0));
            Disciplines.Add(new SC_Discipline("BE", "Site Plan", 1));
            Disciplines.Add(new SC_Discipline("BE", "Demolition", 1));
            Disciplines.Add(new SC_Discipline("BE", "Temporary Works", 1));
            Disciplines.Add(new SC_Discipline("BE", "Power Layout", 2));
            Disciplines.Add(new SC_Discipline("BE", "Lighting Layout", 3));
            Disciplines.Add(new SC_Discipline("BE", "Cable Containment Layout", 4));
            Disciplines.Add(new SC_Discipline("BE", "Emergency Lighting Layout", 5));
            Disciplines.Add(new SC_Discipline("BE", "Electrical Detail Layout", 7));
            Disciplines.Add(new SC_Discipline("BE", "Sections & 3D Views", 8));
            Disciplines.Add(new SC_Discipline("BE", "General Details", 9));

            Disciplines.Add(new SC_Discipline("BF", "Cover Sheet", 0));
            Disciplines.Add(new SC_Discipline("BF", "Drawing Index", 0));
            Disciplines.Add(new SC_Discipline("BF", "Legend", 0));
            Disciplines.Add(new SC_Discipline("BF", "Schematic", 0));
            Disciplines.Add(new SC_Discipline("BF", "Site Plan", 1));
            Disciplines.Add(new SC_Discipline("BF", "Demolition", 1));
            Disciplines.Add(new SC_Discipline("BF", "Temporary Works", 1));
            Disciplines.Add(new SC_Discipline("BF", "Fire Protection Layout", 2));
            Disciplines.Add(new SC_Discipline("BF", "Fire Alarm Layout", 3));
            Disciplines.Add(new SC_Discipline("BF", "Sprinkler Layout", 4));
            Disciplines.Add(new SC_Discipline("BF", "Fire Alarm Detail Layout", 7));
            Disciplines.Add(new SC_Discipline("BF", "Sections & 3D Views", 8));
            Disciplines.Add(new SC_Discipline("BF", "General Details", 9));

            Disciplines.Add(new SC_Discipline("BG", "Cover Sheet", 0));
            Disciplines.Add(new SC_Discipline("BG", "Drawing Index", 0));
            Disciplines.Add(new SC_Discipline("BG", "Legend", 0));
            Disciplines.Add(new SC_Discipline("BG", "Schematic", 0));
            Disciplines.Add(new SC_Discipline("BG", "Site Plan", 1));
            Disciplines.Add(new SC_Discipline("BG", "Demolition", 1));
            Disciplines.Add(new SC_Discipline("BG", "Temporary Works", 1));
            Disciplines.Add(new SC_Discipline("BG", "Medical Gases Layout", 2));
            Disciplines.Add(new SC_Discipline("BG", "Lab Gases Layout", 3));
            Disciplines.Add(new SC_Discipline("BG", "Sections & 3D Views", 8));
            Disciplines.Add(new SC_Discipline("BG", "General Details", 9));

            Disciplines.Add(new SC_Discipline("BH", "Cover Sheet", 0));
            Disciplines.Add(new SC_Discipline("BH", "Drawing Index", 0));
            Disciplines.Add(new SC_Discipline("BH", "Legend", 0));
            Disciplines.Add(new SC_Discipline("BH", "Schematic", 0));
            Disciplines.Add(new SC_Discipline("BH", "Site Plan", 1));
            Disciplines.Add(new SC_Discipline("BH", "Demolition", 1));
            Disciplines.Add(new SC_Discipline("BH", "Temporary Works", 1));
            Disciplines.Add(new SC_Discipline("BH", "HVAC Ductwork Layout", 2));
            Disciplines.Add(new SC_Discipline("BH", "HVAC Pipework Layout", 3));
            Disciplines.Add(new SC_Discipline("BH", "HVAC Detail Layout", 7));
            Disciplines.Add(new SC_Discipline("BH", "Sections & 3D Views", 8));
            Disciplines.Add(new SC_Discipline("BH", "General Details", 9));

            Disciplines.Add(new SC_Discipline("BL", "Cover Sheet", 0));
            Disciplines.Add(new SC_Discipline("BL", "Drawing Index", 0));
            Disciplines.Add(new SC_Discipline("BL", "Legend", 0));
            Disciplines.Add(new SC_Discipline("BL", "Schematic", 0));
            Disciplines.Add(new SC_Discipline("BL", "Site Plan", 1));
            Disciplines.Add(new SC_Discipline("BL", "Demolition", 1));
            Disciplines.Add(new SC_Discipline("BL", "Temporary Works", 1));
            Disciplines.Add(new SC_Discipline("BL", "Vertical Transportation Layout", 2));
            Disciplines.Add(new SC_Discipline("BL", "Sections & 3D Views", 8));
            Disciplines.Add(new SC_Discipline("BL", "General Details", 9));

            Disciplines.Add(new SC_Discipline("BN", "Cover Sheet", 0));
            Disciplines.Add(new SC_Discipline("BN", "Drawing Index", 0));
            Disciplines.Add(new SC_Discipline("BN", "Legend", 0));
            Disciplines.Add(new SC_Discipline("BN", "Schematic", 0));
            Disciplines.Add(new SC_Discipline("BN", "Site Plan", 1));
            Disciplines.Add(new SC_Discipline("BN", "Demolition", 1));
            Disciplines.Add(new SC_Discipline("BN", "Temporary Works", 1));
            Disciplines.Add(new SC_Discipline("BN", "Nurse Call Layout", 2));
            Disciplines.Add(new SC_Discipline("BN", "Sections & 3D Views", 8));
            Disciplines.Add(new SC_Discipline("BN", "General Details", 9));

            Disciplines.Add(new SC_Discipline("BP", "Cover Sheet", 0));
            Disciplines.Add(new SC_Discipline("BP", "Drawing Index", 0));
            Disciplines.Add(new SC_Discipline("BP", "Legend", 0));
            Disciplines.Add(new SC_Discipline("BP", "Schematic", 0));
            Disciplines.Add(new SC_Discipline("BP", "Site Plan", 1));
            Disciplines.Add(new SC_Discipline("BP", "Demolition", 1));
            Disciplines.Add(new SC_Discipline("BP", "Temporary Works", 1));
            Disciplines.Add(new SC_Discipline("BP", "Plumbing Layout", 2));
            Disciplines.Add(new SC_Discipline("BP", "Drainage Layout", 3));
            Disciplines.Add(new SC_Discipline("BP", "Gas Layout", 4));
            Disciplines.Add(new SC_Discipline("BP", "Rainwater Layout", 5));
            Disciplines.Add(new SC_Discipline("BP", "Plumbing & Drainage Detail Layout", 7));
            Disciplines.Add(new SC_Discipline("BP", "Sections & 3D Views", 8));
            Disciplines.Add(new SC_Discipline("BP", "General Details", 9));

            Disciplines.Add(new SC_Discipline("BS", "Cover Sheet", 0));
            Disciplines.Add(new SC_Discipline("BS", "Drawing Index", 0));
            Disciplines.Add(new SC_Discipline("BS", "Legend", 0));
            Disciplines.Add(new SC_Discipline("BS", "Schematic", 0));
            Disciplines.Add(new SC_Discipline("BS", "Site Plan", 1));
            Disciplines.Add(new SC_Discipline("BS", "Demolition", 1));
            Disciplines.Add(new SC_Discipline("BS", "Temporary Works", 1));
            Disciplines.Add(new SC_Discipline("BS", "Security Layout", 2));
            Disciplines.Add(new SC_Discipline("BS", "CCTV Layout", 3));
            Disciplines.Add(new SC_Discipline("BS", "Security Detail Layout", 7));
            Disciplines.Add(new SC_Discipline("BS", "CCTV Camera Views", 8));
            Disciplines.Add(new SC_Discipline("BS", "General Details", 9));

            Disciplines.Add(new SC_Discipline("BV", "Cover Sheet", 0));
            Disciplines.Add(new SC_Discipline("BV", "Drawing Index", 0));
            Disciplines.Add(new SC_Discipline("BV", "Legend", 0));
            Disciplines.Add(new SC_Discipline("BV", "Schematic", 0));
            Disciplines.Add(new SC_Discipline("BV", "Site Plan", 1));
            Disciplines.Add(new SC_Discipline("BV", "Demolition", 1));
            Disciplines.Add(new SC_Discipline("BV", "Temporary Works", 1));
            Disciplines.Add(new SC_Discipline("BV", "AV Layout", 2));
            Disciplines.Add(new SC_Discipline("BV", "Sections & 3D Views", 8));
            Disciplines.Add(new SC_Discipline("BV", "General Details", 9));

            Disciplines.Add(new SC_Discipline("BW", "Cover Sheet", 0));
            Disciplines.Add(new SC_Discipline("BW", "Drawing Index", 0));
            Disciplines.Add(new SC_Discipline("BW", "Legend", 0));
            Disciplines.Add(new SC_Discipline("BW", "Schematic", 0));
            Disciplines.Add(new SC_Discipline("BW", "Site Plan", 1));
            Disciplines.Add(new SC_Discipline("BW", "Demolition", 1));
            Disciplines.Add(new SC_Discipline("BW", "Temporary Works", 1));
            Disciplines.Add(new SC_Discipline("BW", "Pool Water Services Layout", 2));
            Disciplines.Add(new SC_Discipline("BW", "Sections & 3D Views", 8));
            Disciplines.Add(new SC_Discipline("BW", "General Details", 9));
        }
        #endregion

    }
}