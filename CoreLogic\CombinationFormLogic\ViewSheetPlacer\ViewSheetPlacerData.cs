﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using View = Autodesk.Revit.DB.View;



namespace MEP.SheetCreator.CoreLogic.CombinationFormLogic.ViewSheetPlacer
{
    public class ViewSheetPlacerData
    {
        public List<View> Views = new List<View>();
        public List<View> ViewLegends = new List<View>();
        public List<ViewSheet> ViewSheets = new List<ViewSheet>();

        Guid _guidViewGroup = Guid.Parse("19d55866-b017-40ab-92a8-735a8dcbc304");
        List<Viewport> _viewports;

        public ViewSheetPlacerData(Document doc)
        {
            var views = new FilteredElementCollector(doc).OfClass(typeof(View)).Cast<View>().Where(x => !x.Name.Contains("<Revision Schedule>")).ToList();

            _viewports = new FilteredElementCollector(doc).OfClass(typeof(Autodesk.Revit.DB.Viewport)).Cast<Viewport>().ToList();

            foreach (View view in views)
            {
                if (null != view && (!view.IsTemplate))
                {
                    switch (view.ViewType)
                    {
                        //Only these view types would be placed on sheets.
                        case ViewType.ThreeD:
                        case ViewType.FloorPlan:
                        case ViewType.Elevation:
                        case ViewType.Schedule:
                        case ViewType.PanelSchedule:
                        case ViewType.CeilingPlan:
                        case ViewType.DraftingView:
                        case ViewType.Section:
                        case ViewType.EngineeringPlan:
                            Parameter rqViewGroup = view.get_Parameter(_guidViewGroup);
                            bool blViewUsed = false;
                            //going through all placed views
                            foreach (Viewport viewport in _viewports)
                            {
                                //if this view has been placed
                                if (viewport.ViewId == view.Id)
                                {
                                    //Flag view placed, and remove viewport from collection to avoid checking next time.
                                    blViewUsed = true;
                                    _viewports.Remove(viewport);
                                    break;
                                }
                            }
                            //Only add a document view that has not been placed to the view collection
                            if (!blViewUsed)
                            {
                                Views.Add(view);
                            }
                            break;
                        case ViewType.Legend:
                            ViewLegends.Add(view);
                            break;
                        case ViewType.DrawingSheet:
                            ViewSheet rqViewSheet = view as ViewSheet;
                            //Place holder sheets won't be collected
                            if (!rqViewSheet.IsPlaceholder)
                            {
                                ViewSheets.Add(rqViewSheet);
                            }
                            break;
                        default:
                            break;
                    }
                }
            }

        }
    }

}
