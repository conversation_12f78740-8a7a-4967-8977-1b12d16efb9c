# Category Visibility Restoration Fix

## Problem
When optimizing viewport creation by hiding categories, the categories remained hidden in both the view and the viewport after the process completed, even though they should be visible.

## Root Cause
The optimization code was:
1. Hiding all categories in the view to speed up viewport creation
2. Creating the viewport (which inherits the hidden category state)
3. **NOT restoring** the original category visibility states

This resulted in:
- ✅ Fast viewport creation (performance goal achieved)
- ❌ Categories permanently hidden in view and viewport
- ❌ User has to manually show categories again

## Solution: Store and Restore Category Visibility

### 1. Store Original Visibility States
```csharp
// Store original category visibility states
var originalCategoryVisibility = new Dictionary<ElementId, bool>();

foreach (Category category in doc.Settings.Categories)
{
    if (category != null && view.CanCategoryBeHidden(category.Id))
    {
        // Store original visibility state (true = visible, false = hidden)
        originalCategoryVisibility[category.Id] = !view.GetCategoryHidden(category.Id);
        // Hide category to speed up viewport creation
        view.SetCategoryHidden(category.Id, true);
    }
}
```

### 2. Create Viewport with Hidden Categories
```csharp
// Create viewport with simplified view (faster regeneration)
var viewport = Viewport.Create(doc, sheetId, view.Id, location);
```

### 3. Restore View Category Visibility
```csharp
// Restore original category visibility in the view
foreach (var kvp in originalCategoryVisibility)
{
    if (view.CanCategoryBeHidden(kvp.Key))
    {
        view.SetCategoryHidden(kvp.Key, !kvp.Value); // Restore original state
    }
}
```

### 4. Restore Viewport Category Visibility
```csharp
// Also ensure viewport shows all categories that should be visible
RestoreViewportCategoryVisibility(viewport, originalCategoryVisibility);
```

## Key Implementation Details

### Dictionary Storage Pattern
```csharp
// Store visibility: true = was visible, false = was hidden
originalCategoryVisibility[category.Id] = !view.GetCategoryHidden(category.Id);

// Restore visibility: if was visible (true), set hidden to false
view.SetCategoryHidden(kvp.Key, !kvp.Value);
```

### Viewport-Specific Restoration
The viewport inherits category visibility from the view at creation time, but may need separate restoration:

```csharp
private static void RestoreViewportCategoryVisibility(Viewport viewport, Dictionary<ElementId, bool> originalVisibility)
{
    foreach (var kvp in originalVisibility)
    {
        ElementId categoryId = kvp.Key;
        bool shouldBeVisible = kvp.Value;

        if (viewport.CanCategoryBeHidden(categoryId))
        {
            viewport.SetCategoryHidden(categoryId, !shouldBeVisible);
        }
    }
}
```

## Benefits of the Fix

1. **Maintains Performance**: Categories are still hidden during viewport creation for speed
2. **Restores User Experience**: All categories return to their original visibility state
3. **Handles Both View and Viewport**: Ensures consistency between view and sheet display
4. **Error Resilient**: Graceful handling if category restoration fails
5. **Preserves Original State**: Doesn't assume all categories should be visible

## Performance Impact

- **During Creation**: Maximum speed (all categories hidden)
- **After Creation**: Full restoration to original state
- **Net Result**: Fast creation + correct final display

## Testing Recommendations

1. **Test with various category states**:
   - Some categories hidden, some visible
   - All categories visible
   - All categories hidden

2. **Verify restoration**:
   - Check view category visibility after viewport creation
   - Check viewport category visibility on sheet
   - Ensure both match original states

3. **Test error scenarios**:
   - Categories that can't be hidden/shown
   - Views with complex category overrides
   - Views with view templates that control categories

The fix ensures that your excellent performance optimization doesn't compromise the user experience by permanently hiding categories.
