﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="Current" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug 2014|AnyCPU'">
    <StartProgram>C:\Program Files\Autodesk\Revit 2014\Revit.exe</StartProgram>
    <StartAction>Program</StartAction>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug 2015|AnyCPU'">
    <StartProgram>C:\Program Files\Autodesk\Revit 2015\Revit.exe</StartProgram>
    <StartAction>Program</StartAction>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug 2016|AnyCPU'">
    <StartProgram>C:\Program Files\Autodesk\Revit 2016\Revit.exe</StartProgram>
    <StartAction>Program</StartAction>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug 2017|AnyCPU'">
    <StartProgram>C:\Program Files\Autodesk\Revit 2017\Revit.exe</StartProgram>
    <StartAction>Program</StartAction>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug 2018|AnyCPU'">
    <StartProgram>C:\Program Files\Autodesk\Revit 2018\Revit.exe</StartProgram>
    <StartAction>Program</StartAction>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug 2020|AnyCPU'">
    <StartProgram>C:\Program Files\Autodesk\Revit 2020\Revit.exe</StartProgram>
    <StartAction>Program</StartAction>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug 2021|AnyCPU'">
    <StartProgram>C:\Program Files\Autodesk\Revit 2021\Revit.exe</StartProgram>
    <StartAction>Program</StartAction>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug 2022|AnyCPU'">
    <StartProgram>C:\Program Files\Autodesk\Revit 2022\Revit.exe</StartProgram>
    <StartAction>Program</StartAction>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release 2014|AnyCPU'">
    <StartProgram>C:\Program Files\Autodesk\Revit 2014\Revit.exe</StartProgram>
    <StartAction>Program</StartAction>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release 2015|AnyCPU'">
    <StartProgram>C:\Program Files\Autodesk\Revit 2015\Revit.exe</StartProgram>
    <StartAction>Program</StartAction>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release 2016|AnyCPU'">
    <StartProgram>C:\Program Files\Autodesk\Revit 2016\Revit.exe</StartProgram>
    <StartAction>Program</StartAction>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release 2017|AnyCPU'">
    <StartProgram>C:\Program Files\Autodesk\Revit 2017\Revit.exe</StartProgram>
    <StartAction>Program</StartAction>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release 2018|AnyCPU'">
    <StartProgram>C:\Program Files\Autodesk\Revit 2018\Revit.exe</StartProgram>
    <StartAction>Program</StartAction>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release 2019|AnyCPU'">
    <StartProgram>C:\Program Files\Autodesk\Revit 2019\Revit.exe</StartProgram>
    <StartAction>Program</StartAction>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release 2020|AnyCPU'">
    <StartProgram>C:\Program Files\Autodesk\Revit 2020\Revit.exe</StartProgram>
    <StartAction>Program</StartAction>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release 2021|AnyCPU'">
    <StartProgram>C:\Program Files\Autodesk\Revit 2021\Revit.exe</StartProgram>
    <StartAction>Program</StartAction>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release 2022|AnyCPU'">
    <StartProgram>C:\Program Files\Autodesk\Revit 2022\Revit.exe</StartProgram>
    <StartAction>Program</StartAction>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug 2019|AnyCPU'">
    <StartProgram>C:\Program Files\Autodesk\Revit 2019\Revit.exe</StartProgram>
    <StartAction>Program</StartAction>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug 2023|AnyCPU'">
    <StartProgram>C:\Program Files\Autodesk\Revit 2023\Revit.exe</StartProgram>
    <StartAction>Program</StartAction>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug 2024|AnyCPU'">
    <StartProgram>C:\Program Files\Autodesk\Revit 2024\Revit.exe</StartProgram>
    <StartAction>Program</StartAction>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release 2023|AnyCPU'">
    <StartProgram>C:\Program Files\Autodesk\Revit 2023\Revit.exe</StartProgram>
    <StartAction>Program</StartAction>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release 2024|AnyCPU'">
    <StartProgram>C:\Program Files\Autodesk\Revit 2024\Revit.exe</StartProgram>
    <StartAction>Program</StartAction>
  </PropertyGroup>
  <PropertyGroup>
    <ReferencePath>
    </ReferencePath>
    <ProjectView>ShowAllFiles</ProjectView>
  </PropertyGroup>
  <ItemGroup>
    <Compile Update="UI\Forms\FrmLocationPreview.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="UI\Forms\FrmParameterList.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="UI\Forms\FrmSecondaryViewTemplate.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="UI\Forms\FrmSheetAndViewCreator.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="UI\Forms\FrmSheetCreatorHelp.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="UI\Forms\FrmViewSheetCreator.cs">
      <SubType>Form</SubType>
    </Compile>
  </ItemGroup>
</Project>