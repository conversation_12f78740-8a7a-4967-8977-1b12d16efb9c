﻿namespace MEP.SheetCreator.UI.Forms
{
    partial class FrmSheetCreatorHelp
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.SuspendLayout();
            // 
            // FrmSheetCreatorHelp
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 16F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.CheatSheetLink = "";
            this.ClientSize = new System.Drawing.Size(1381, 703);
            this.lbl_ApporvedNamesText = "Tristan Balme";
            this.lbl_CheckedNamesText = "<PERSON> Roberts";
            this.lbl_DevelopedNamesText = "Firza Utama, Mitchell Roberts";
            this.lbl_ToolFullNameText = "Beca Tools | Building Services | View Sheet Creator";
            this.lbl_VersionText = "Version 2.00.00";
            this.lbl_YearText = "© Beca 2022";
            this.Margin = new System.Windows.Forms.Padding(7, 6, 7, 6);
            this.MinimumSize = new System.Drawing.Size(1397, 739);
            this.Name = "FrmSheetCreatorHelp";
            this.Text = "Beca Tools | Building Services";
            this.TitleText = "VIEW SHEET CREATOR - HELP";
            this.UserGuideLink = "";
            this.VerisionText = "© 2022   02.00.00  ";
            this.VersionHistoryText = "2021.04.29 Alpha version\n2021.07.30 Beta version\n2022.06.20 Combine Batch2DView, " +
    "DependentView, ViewSheetPlacer\n\n";
            this.VideoLink = "";
            this.WikiPageLink = "https://becagroup.sharepoint.com/KnowledgeCentre/Buildings/BIMBrilliance/Pages/Sh" +
    "eet%20and%20View%20Creator.aspx";
            this.ResumeLayout(false);

        }

        #endregion
    }
}