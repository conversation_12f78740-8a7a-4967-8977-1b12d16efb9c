﻿using Autodesk.Revit.DB;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using View = Autodesk.Revit.DB.View;


namespace MEP.SheetCreator.CoreLogic
{
    public class VSC_ViewTemplate
    {
        #region Fields
        public View View;
        public bool Selected { get; set; }
        public string ViewType { get; set; }
        public string Name { get; set; }
        public string DrawingType { get; set; }
        public string Secondary { get; set; }
        public string DisciplineName { get; set; }
        

        #endregion

        #region Properties



        #endregion

        #region Constructors
        public VSC_ViewTemplate(View v)
        {
            View = v;
            Selected = false;
            Name = v.Name;
            ViewType = v.ViewType.ToString();
            DrawingType = "";
            Secondary = "";
            DisciplineName = "";
            
        }
        #endregion

        #region Methods

        #endregion
    }
}
