﻿using Autodesk.Revit.DB;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.SheetCreator.CoreLogic
{
    public class VSC_ScopeBoxScaleFactor
    {
        public Element ScopeBox;
        public string Name;
        public double XScaleFactor;
        public double YScaleFactor;

        XYZ OverallScopeBoxCenterPoint;
        XYZ ZoneScopeBoxCenterPoint;

        public VSC_ScopeBoxScaleFactor(Element overallScopeBox, Element zoneScopeBox)
        {
            ScopeBox = zoneScopeBox;
            Name = ScopeBox.Name;

            OverallScopeBoxCenterPoint = GetCenterPoint(overallScopeBox);
            ZoneScopeBoxCenterPoint = GetCenterPoint(zoneScopeBox);

            XScaleFactor = GetScaleFactor(ZoneScopeBoxCenterPoint.X, OverallScopeBoxCenterPoint.X );
            YScaleFactor = GetScaleFactor(ZoneScopeBoxCenterPoint.Y, OverallScopeBoxCenterPoint.Y );
        }

        private XYZ GetCenterPoint(Element scopeBox)
        {
            var bb = scopeBox.get_BoundingBox(null);
            return bb.Min.Add(bb.Max).Multiply(0.5);
        }

        private double GetScaleFactor(double ZoneScopeBoxCenter, double OverallScopeBoxCenter)
        {
            return ZoneScopeBoxCenter / OverallScopeBoxCenter;
            //if (Pf > 1)
            //    return OverallScopeBoxCenter / Pf;
            //else
            //    return OverallScopeBoxCenter * Pf;
        }
        
    }
}
