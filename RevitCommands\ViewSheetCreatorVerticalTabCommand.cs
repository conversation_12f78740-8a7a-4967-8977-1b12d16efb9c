﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using BecaCommand;
using MEP.SheetCreator.CoreLogic;
using MEP.SheetCreator.CoreLogic.CombinationFormLogic.Batch2DView;
using MEP.SheetCreator.UI.Forms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.SheetCreator.RevitCommands
{
    [Transaction(TransactionMode.Manual)]
    class ViewSheetCreatorVerticalTabCommand : BecaBaseCommand
    {
        public override Result ExecuteBecaCommand(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            UIApplication uiapp = commandData.Application;
            UIDocument uidoc = uiapp.ActiveUIDocument;
            var app = uiapp.Application;
            Document doc = uidoc.Document;

            _taskLogger.PreTaskStart();

            using (var frmSC = new FrmSheetAndViewCreator(app, doc))
            {
                frmSC.ShowDialog();
            }

            _taskLogger.PostTaskEnd("View Sheet Creator completed.");

            return Result.Succeeded;
        }

        public override string GetAddinAuthor()
        {
            return "Firza Utama";
        }

        public override string GetAddinName()
        {
            return AddinNames.ViewAndSheetCreator.Value;
        }

        public override string GetCommandSubName()
        {
            return "View Sheet Creator";
        }
    }
}
