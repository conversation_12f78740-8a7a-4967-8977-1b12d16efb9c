﻿<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<UseWindowsForms>true</UseWindowsForms>
		<Configurations>Debug R20;Debug R21;Debug R22;Debug R23;Debug R24;Debug R25;Release R20;Release R21;Release R22;Release R23;Release R24;Release R25;Debug R26;Release R26</Configurations>
	</PropertyGroup>
	<ItemGroup>
		<Compile Remove="UI\ModelessRevitForm\**" />
		<EmbeddedResource Remove="UI\ModelessRevitForm\**" />
		<None Remove="UI\ModelessRevitForm\**" />
	</ItemGroup>
	
	<ItemGroup>
		<ProjectReference Include="..\..\COMMON\BecaActivityLogger\BecaActivityLogger.csproj" />
		<ProjectReference Include="..\..\COMMON\BecaCommand\BecaCommand.csproj" />
		<ProjectReference Include="..\..\COMMON\BecaRevitUtilities\BecaRevitUtilities.csproj" />
		<ProjectReference Include="..\..\COMMON\BecaTransactionsNames\BecaTransactionsNamesManager.csproj" />
		<ProjectReference Include="..\..\COMMON\BecaTrekaHandler\BecaTrekaHandler.csproj" />
		<ProjectReference Include="..\..\COMMON\Common.EnhancedADGV\Common.EnhancedADGV.csproj" />
		<ProjectReference Include="..\..\COMMON\Common.Extenstions\Common.Extenstions.csproj" />
		<ProjectReference Include="..\..\COMMON\Common.UI\Common.UI.csproj" />
		<ProjectReference Include="..\..\COMMON\Common.Utilities\Common.Utilities.csproj" />
	</ItemGroup>
	<ItemGroup>
		<Reference Include="Common.BecaLicense">
			<HintPath>..\..\3rdParties\BecaLicensing\Common.BecaLicense.dll</HintPath>
		</Reference>
		<Reference Include="PresentationCore" />
		<Reference Include="PresentationFramework" />
		<Reference Include="WindowsFormsIntegration" />
	</ItemGroup>

	<ItemGroup Condition="'$(TargetFramework)' == 'net8.0-windows'">
		<PackageReference Include="System.DirectoryServices.AccountManagement" Version="8.0.1" />
	</ItemGroup>
	
	
	<ItemGroup Condition="'$(TargetFramework)' == 'net48'">
		<Reference Include="System.DirectoryServices.AccountManagement">
			<HintPath>..\..\..\..\..\..\..\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.DirectoryServices.AccountManagement.dll</HintPath>
		</Reference>
	</ItemGroup>
</Project>