﻿using Autodesk.Revit.DB;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MessageBox = System.Windows.MessageBox;


namespace MEP.SheetCreator.CoreLogic.CombinationFormLogic.SheetCreator
{
    public static class SheetCreatorLogic
    {
        public static bool RunLogic(Document doc, List<SheetCreatorItem> sheetCreatorItems)
        {
            try
            {
                // Create Sheets
                using (var trans = new Transaction(doc, "Create Sheets"))
                {
                    trans.Start();
                    foreach (var item in sheetCreatorItems)
                    {
                        try
                        {
                            item.SuccessfullyCreated = false;
                            var createdSheet = CreateSheet(doc, item.TitleblockId, item.SheetName, item.SheetNumber);
                            if (createdSheet != null)
                            {
                                item.SuccessfullyCreated = true;
                                item.CreatedSheet = createdSheet;
                            }
                        }
                        catch (Exception)
                        {
                            item.SuccessfullyCreated= false;
                        }
                    }
                    trans.Commit();
                }
                
                return true;
            }
            catch (Exception e)
            {
                System.Windows.Forms.MessageBox.Show(e.Message);
                return false;
            }
            
        }

        private static ViewSheet CreateSheet(Document doc, ElementId titleblockId, string sheetName, string sheetNumber)
        {
            if (string.IsNullOrEmpty(sheetName) || string.IsNullOrEmpty(sheetNumber))
                return null;
            else
            {
                ViewSheet vs;
                try
                {
                    vs = ViewSheet.Create(doc, titleblockId);
                    vs.Name = sheetName;
                    vs.SheetNumber = sheetNumber;
                    return vs;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"{ex.Message} \n\nSheet name: {sheetNumber}\nSheet number: {sheetName}");
                    return null;
                }
                
            }
            
        }
    }

    public class SheetCreatorItem
    {
        public bool SuccessfullyCreated { get; set; }
        public ViewSheet CreatedSheet { get; set; }
        public string SheetName { get; set; }
        public string SheetNumber { get; set; }
        public ElementId TitleblockId { get; set; }

        public List<ParameterValue> ParametersAndValues = new List<ParameterValue>();
    }

    public class ParameterValue
    {
        public string Parameter { get; set; }
        public string Value { get; set; }
    }
}
