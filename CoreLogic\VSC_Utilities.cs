using Autodesk.Revit.DB;
using BecaRevitUtilities.Collectors;
using MEP.SheetCreator.CoreLogic.CombinationFormLogic.VSC_2;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using View = Autodesk.Revit.DB.View;


namespace MEP.SheetCreator.CoreLogic
{
    public static class VSC_Utilities
    {
        #region Methods
        public static string ExtractSheetNumberFromVT(string discipline, string sequenceNumber, string editedLevelName, string selectedScopeBox)
        {
            return discipline + "-" + sequenceNumber + editedLevelName + selectedScopeBox;
        }
        public static string ExtractSheetNameFromVT(string prefix, string editedLevelName, string drawingType, string selectedScopeBox, string suffix)
        {
            return prefix + editedLevelName + " " + drawingType + " " + selectedScopeBox + suffix;

        }

        public static string ExtractViewNameFromVT(string prefix, string editedLevelName, string drawingType, string selectedScopeBox, string scale, string suffix)
        {
            return prefix + editedLevelName + " " + drawingType + " " + selectedScopeBox +" "+ scale + suffix;

        }

        public static string ExtractBaseViewNameFromVT(string prefix, string editedLevelName, string drawingType)
        {
            return prefix + " " + editedLevelName + " " + drawingType;

        }

        public static ViewSheet CreateSheet(Document doc, ElementId titleblockId, string sheetName, string sheetNumber)
        {
            ViewSheet vs;
            vs = ViewSheet.Create(doc, titleblockId);
            vs.Name = sheetName;
            vs.SheetNumber = sheetNumber;
            return vs;
        }

        public static void SetBecaSheetParameters(ViewSheet sheet, VSC_CreationItems items)
        {
            sheet.LookupParameter("Beca Discipline")?.Set(items.DisciplineName);
            sheet.LookupParameter("Beca Drawn")?.Set(items.DrawnBy);
            sheet.LookupParameter("Beca Drawn Date")?.Set(items.Date);
            sheet.LookupParameter("Beca Design Date")?.Set(items.Date);
            sheet.LookupParameter("Beca Designed")?.Set(items.Designer);
        }

        public static void VSC_2_SetBecaSheetParameters(ViewSheet sheet, VSC_2_CreationItems items)
        {
            sheet.LookupParameter("Beca Discipline")?.Set(items.DisciplineName);
            sheet.LookupParameter("Beca Drawn")?.Set(items.DrawnBy);
            sheet.LookupParameter("Beca Designed")?.Set(items.Designer);
            sheet.LookupParameter("Beca Verifier")?.Set(items.VerifiedBy);
            sheet.LookupParameter("Beca Drawn Date")?.Set(items.Date);
            sheet.LookupParameter("Beca Design Date")?.Set(items.Date);
            sheet.LookupParameter("Beca Verify Date")?.Set(items.Date);
            
            
        }

        public static void SetBecaTitleblockParameters(Document doc, ViewSheet sheet)
        {
            var titleBlock = new FilteredElementCollector(doc, sheet.Id).OfCategory(BuiltInCategory.OST_TitleBlocks).First() as FamilyInstance;
            titleBlock.LookupParameter("Check Print Visibility")?.Set("No");
        }

        public static ViewPlan CreateView(Document doc, ElementId viewFamilyTypeId, ElementId levelId, string viewName)
        {
            var view = ViewPlan.Create(doc, viewFamilyTypeId, levelId);
            view.Name = viewName;
            return view;
        }

        /// <summary>
        /// Creates a viewport with performance optimization techniques for large models
        /// </summary>
        /// <param name="doc">The document</param>
        /// <param name="sheetId">The sheet ID</param>
        /// <param name="viewId">The view ID</param>
        /// <param name="location">The placement location</param>
        /// <param name="useOptimization">Whether to use performance optimization</param>
        /// <returns>The created viewport</returns>
        public static Viewport CreateOptimizedViewport(Document doc, ElementId sheetId, ElementId viewId, XYZ location, bool useOptimization = true)
        {
            if (!useOptimization)
            {
                return Viewport.Create(doc, sheetId, viewId, location);
            }

            var view = doc.GetElement(viewId) as View;
            if (view == null)
            {
                return Viewport.Create(doc, sheetId, viewId, location);
            }

            // Check if view has many elements that might slow down regeneration
            var elementCount = new FilteredElementCollector(doc, viewId)
                .WhereElementIsNotElementType()
                .GetElementCount();

            // For views with many elements, use lightweight optimization
            if (elementCount > 50) // Configurable threshold
            {
                return CreateViewportWithLightweightOptimization(doc, sheetId, view, location);
            }
            else
            {
                return Viewport.Create(doc, sheetId, viewId, location);
            }
        }

        /// <summary>
        /// Creates viewport using lightweight optimization that works within existing transactions
        /// </summary>
        private static Viewport CreateViewportWithLightweightOptimization(Document doc, ElementId sheetId, View view, XYZ location)
        {
            Viewport viewport = null;

            try
            {
                // Check if we're already in a transaction
                bool inTransaction = doc.IsModifiable;

                if (inTransaction)
                {
                    // We're in an active transaction, use simple optimization
                    viewport = CreateViewportWithSimpleOptimization(doc, sheetId, view, location);
                }
                else
                {
                    // We can create our own transaction for more advanced optimization
                    viewport = CreateViewportWithAdvancedOptimization(doc, sheetId, view, location);
                }
            }
            catch
            {
                // If optimization fails, use standard creation
                try
                {
                    viewport = Viewport.Create(doc, sheetId, view.Id, location);
                }
                catch
                {
                    // Return null if even standard creation fails
                    viewport = null;
                }
            }

            return viewport;
        }

        /// <summary>
        /// Simple optimization that works within existing transactions
        /// </summary>
        private static Viewport CreateViewportWithSimpleOptimization(Document doc, ElementId sheetId, View view, XYZ location)
        {
            // For views within active transactions, we can't use isolation
            // but we can still apply some optimizations

            try
            {
                // Temporarily disable view template if it's complex
                ElementId originalTemplate = ElementId.InvalidElementId;
                var templateParam = view.get_Parameter(BuiltInParameter.VIEW_TEMPLATE);
                if (templateParam != null && templateParam.AsElementId() != ElementId.InvalidElementId)
                {
                    originalTemplate = templateParam.AsElementId();
                    // Temporarily remove view template to speed up regeneration
                    templateParam.Set(ElementId.InvalidElementId);
                }

                // Create viewport with simplified view
                var viewport = Viewport.Create(doc, sheetId, view.Id, location);

                // Restore view template
                if (originalTemplate != ElementId.InvalidElementId)
                {
                    templateParam.Set(originalTemplate);
                }

                return viewport;
            }
            catch
            {
                // Fallback to standard creation
                return Viewport.Create(doc, sheetId, view.Id, location);
            }
        }

        /// <summary>
        /// Advanced optimization for use outside of transactions
        /// </summary>
        private static Viewport CreateViewportWithAdvancedOptimization(Document doc, ElementId sheetId, View view, XYZ location)
        {
            Viewport viewport = null;

            try
            {
                using (Transaction tx = new Transaction(doc, "Optimized Viewport Creation"))
                {
                    tx.Start();

                    // Get critical elements for potential isolation
                    var criticalElements = GetCriticalElementsForView(doc, view);

                    if (criticalElements.Count > 0 && criticalElements.Count < 1000) // Reasonable limit
                    {
                        // Temporarily isolate elements to reduce regeneration load
                        view.IsolateElementsTemporary(criticalElements);

                        // Create viewport with isolated view
                        viewport = Viewport.Create(doc, sheetId, view.Id, location);

                        // Clean up isolation
                        if (view.IsTemporaryHideIsolateActive())
                        {
                            view.DisableTemporaryViewMode(TemporaryViewMode.TemporaryHideIsolate);
                        }
                    }
                    else
                    {
                        // Standard creation if isolation isn't beneficial
                        viewport = Viewport.Create(doc, sheetId, view.Id, location);
                    }

                    tx.Commit();
                }
            }
            catch
            {
                // Fallback to standard creation
                using (Transaction tx = new Transaction(doc, "Standard Viewport Creation"))
                {
                    tx.Start();
                    viewport = Viewport.Create(doc, sheetId, view.Id, location);
                    tx.Commit();
                }
            }

            return viewport;
        }

        /// <summary>
        /// Gets critical elements that should remain visible during viewport creation
        /// </summary>
        private static ICollection<ElementId> GetCriticalElementsForView(Document doc, View view)
        {
            var criticalElements = new List<ElementId>();

            try
            {
                // Focus on structural and architectural elements that define the space
                var criticalCategories = new[]
                {
                    BuiltInCategory.OST_Walls,
                    BuiltInCategory.OST_Floors,
                    BuiltInCategory.OST_Roofs,
                    BuiltInCategory.OST_Columns,
                    BuiltInCategory.OST_StructuralFraming
                };

                foreach (var category in criticalCategories)
                {
                    try
                    {
                        var elements = new FilteredElementCollector(doc, view.Id)
                            .OfCategory(category)
                            .WhereElementIsNotElementType()
                            .ToElementIds();

                        // Take a subset to keep regeneration fast
                        criticalElements.AddRange(elements.Take(20)); // Limit per category
                    }
                    catch
                    {
                        // Skip categories that don't exist in this view
                        continue;
                    }
                }

                // If no critical elements found, get a small subset of any elements
                if (criticalElements.Count == 0)
                {
                    var anyElements = new FilteredElementCollector(doc, view.Id)
                        .WhereElementIsNotElementType()
                        .ToElementIds()
                        .Take(10); // Very small subset

                    criticalElements.AddRange(anyElements);
                }
            }
            catch
            {
                // Return empty if anything fails
                criticalElements.Clear();
            }

            return criticalElements;
        }

        public static void NotIncludeVTParameter(View viewTemplate, string scaleParameter, string secondaryVTParameter)
        {
            var viewparams = new List<Parameter>();
            foreach (Parameter p in viewTemplate.Parameters)
                viewparams.Add(p);

            var ids = new List<ElementId>();
            if (viewTemplate.LookupParameter(secondaryVTParameter) != null)
                ids.Add(viewparams.Where(p => p.Definition.Name == secondaryVTParameter)?.First().Id);
            ids.Add(viewparams.Where(p => p.Definition.Name == scaleParameter).First().Id);
            
            viewTemplate.SetNonControlledTemplateParameterIds(ids);

        }

        public static double GetScaleFactor(double OverallScopeBoxCenter, double scaleFactor)
        {
            if (scaleFactor > 1)
                return OverallScopeBoxCenter / scaleFactor;
            else
                return OverallScopeBoxCenter * scaleFactor;
        }

        #endregion

    }
}