﻿using Autodesk.Revit.DB;
using BecaRevitUtilities.Collectors;
using MEP.SheetCreator.CoreLogic.CombinationFormLogic.VSC_2;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using View = Autodesk.Revit.DB.View;


namespace MEP.SheetCreator.CoreLogic
{
    public static class VSC_Utilities
    {
        #region Methods
        public static string ExtractSheetNumberFromVT(string discipline, string sequenceNumber, string editedLevelName, string selectedScopeBox)
        {
            return discipline + "-" + sequenceNumber + editedLevelName + selectedScopeBox;
        }
        public static string ExtractSheetNameFromVT(string prefix, string editedLevelName, string drawingType, string selectedScopeBox, string suffix)
        {
            return prefix + editedLevelName + " " + drawingType + " " + selectedScopeBox + suffix;

        }

        public static string ExtractViewNameFromVT(string prefix, string editedLevelName, string drawingType, string selectedScopeBox, string scale, string suffix)
        {
            return prefix + editedLevelName + " " + drawingType + " " + selectedScopeBox +" "+ scale + suffix;

        }

        public static string ExtractBaseViewNameFromVT(string prefix, string editedLevelName, string drawingType)
        {
            return prefix + " " + editedLevelName + " " + drawingType;

        }

        public static ViewSheet CreateSheet(Document doc, ElementId titleblockId, string sheetName, string sheetNumber)
        {
            ViewSheet vs;
            vs = ViewSheet.Create(doc, titleblockId);
            vs.Name = sheetName;
            vs.SheetNumber = sheetNumber;
            return vs;
        }

        public static void SetBecaSheetParameters(ViewSheet sheet, VSC_CreationItems items)
        {
            sheet.LookupParameter("Beca Discipline")?.Set(items.DisciplineName);
            sheet.LookupParameter("Beca Drawn")?.Set(items.DrawnBy);
            sheet.LookupParameter("Beca Drawn Date")?.Set(items.Date);
            sheet.LookupParameter("Beca Design Date")?.Set(items.Date);
            sheet.LookupParameter("Beca Designed")?.Set(items.Designer);
        }

        public static void VSC_2_SetBecaSheetParameters(ViewSheet sheet, VSC_2_CreationItems items)
        {
            sheet.LookupParameter("Beca Discipline")?.Set(items.DisciplineName);
            sheet.LookupParameter("Beca Drawn")?.Set(items.DrawnBy);
            sheet.LookupParameter("Beca Designed")?.Set(items.Designer);
            sheet.LookupParameter("Beca Verifier")?.Set(items.VerifiedBy);
            sheet.LookupParameter("Beca Drawn Date")?.Set(items.Date);
            sheet.LookupParameter("Beca Design Date")?.Set(items.Date);
            sheet.LookupParameter("Beca Verify Date")?.Set(items.Date);
            
            
        }

        public static void SetBecaTitleblockParameters(Document doc, ViewSheet sheet)
        {
            var titleBlock = new FilteredElementCollector(doc, sheet.Id).OfCategory(BuiltInCategory.OST_TitleBlocks).First() as FamilyInstance;
            titleBlock.LookupParameter("Check Print Visibility")?.Set("No");
        }

        public static ViewPlan CreateView(Document doc, ElementId viewFamilyTypeId, ElementId levelId, string viewName)
        {
            var view = ViewPlan.Create(doc, viewFamilyTypeId, levelId);
            view.Name = viewName;
            return view;
        }

        public static void NotIncludeVTParameter(View viewTemplate, string scaleParameter, string secondaryVTParameter)
        {
            var viewparams = new List<Parameter>();
            foreach (Parameter p in viewTemplate.Parameters)
                viewparams.Add(p);

            var ids = new List<ElementId>();
            if (viewTemplate.LookupParameter(secondaryVTParameter) != null)
                ids.Add(viewparams.Where(p => p.Definition.Name == secondaryVTParameter)?.First().Id);
            ids.Add(viewparams.Where(p => p.Definition.Name == scaleParameter).First().Id);
            
            viewTemplate.SetNonControlledTemplateParameterIds(ids);

        }

        public static double GetScaleFactor(double OverallScopeBoxCenter, double scaleFactor)
        {
            if (scaleFactor > 1)
                return OverallScopeBoxCenter / scaleFactor;
            else
                return OverallScopeBoxCenter * scaleFactor;
        }

        #endregion

    }
}