﻿using Autodesk.Revit.DB;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.SheetCreator.CoreLogic.CombinationFormLogic.VSC_2
{
    public class VSC_TitleblockParameters
    {
        public int OriginalInColour { get; set; }
        public int InterimIssue { get; set; }
        public int BecaCheckPrintVisibility { get; set; }
    }
}
