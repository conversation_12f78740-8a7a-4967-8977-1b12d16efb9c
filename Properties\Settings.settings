﻿<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="MEP.SheetCreator.Properties" GeneratedClassName="Settings">
  <Profiles />
  <Settings>
    <Setting Name="strViewOriginX" Type="System.String" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
    <Setting Name="strViewOriginY" Type="System.String" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
    <Setting Name="strLgdOriginX" Type="System.String" Scope="User">
      <Value Profile="(Default)">718</Value>
    </Setting>
    <Setting Name="strLgdOriginY" Type="System.String" Scope="User">
      <Value Profile="(Default)">570</Value>
    </Setting>
    <Setting Name="Spliter1Distance" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">291</Value>
    </Setting>
    <Setting Name="Spliter2Distance" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">273</Value>
    </Setting>
  </Settings>
</SettingsFile>