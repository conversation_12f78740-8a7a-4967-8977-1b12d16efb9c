﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MEP.SheetCreator.UI.UIData
{
    internal static class AdgvColumnsBinding
    {
        public static void TitleBlocksData(Zuby.ADGV.AdvancedDataGridView adgvFittingsCount, BindingSource bindingSource)
        {
            adgvFittingsCount.DataSource = bindingSource;

            adgvFittingsCount.Columns[0].Name = "TitleBlocks";
            adgvFittingsCount.Columns["TitleBlocks"].HeaderText = "\nTitle Blocks\n";
        }

        public static void ViewTemplateData(Zuby.ADGV.AdvancedDataGridView adgv_01_AvailableVT, BindingSource bs_AvailableVT)
        {
            adgv_01_AvailableVT.DataSource = bs_AvailableVT;

            adgv_01_AvailableVT.Columns[0].Name = "ViewType";
            adgv_01_AvailableVT.Columns["ViewType"].HeaderText = "View Type\n";
            adgv_01_AvailableVT.Columns[1].Name = "Name";
            adgv_01_AvailableVT.Columns["Name"].HeaderText = "Name\n";
            adgv_01_AvailableVT.Columns[2].Name = "Secondary";
            adgv_01_AvailableVT.Columns["Secondary"].HeaderText = "Secondary\n";

        }

        public static void ParentViewData(Zuby.ADGV.AdvancedDataGridView adgv_02_ParentView, BindingSource bs_ParentView)
        {
            adgv_02_ParentView.DataSource = bs_ParentView;

            adgv_02_ParentView.Columns[0].Name = "ViewType";
            adgv_02_ParentView.Columns["ViewType"].HeaderText = "View Type\n";
            adgv_02_ParentView.Columns["ViewType"].Width = 100;
            adgv_02_ParentView.Columns[1].Name = "Name";
            adgv_02_ParentView.Columns["Name"].HeaderText = "Name\n";

        }
    }
}
