using Autodesk.Revit.ApplicationServices;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using Common.UI.Forms;
using MEP.SheetCreator.UI.Forms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Application = Autodesk.Revit.ApplicationServices.Application;
using TaskDialog = Autodesk.Revit.UI.TaskDialog;
using View = Autodesk.Revit.DB.View;


namespace MEP.SheetCreator.CoreLogic
{
    public static class VSC_AdvancedFormCoreLogic
    {
        public static bool RunLogic(Application app, Document doc)
        {
            #region Data Preparation and Form
            var version = app.VersionNumber;
            var overallName = "OVERALL";
            var parentName = "PARENT";
            var scaleParameter = "View Scale";
            var secondaryVTParameter = "Beca_SecondaryViewTemplate";
            var data = new VSC_Data(doc);

            if (data.ScopeBoxes.Count() == 0)
            {
                TaskDialog.Show("Null Scope Box", "No Scope Box found.\nPlease create Overall and Zone Scope Boxes.");
                return false;
            }
            if (data.ViewTemplates.Count() == 0)
            {
                TaskDialog.Show("Null View Template", "No View Template found.\nThis tool will not work without View Template.");
                return false;
            }

            var frmSC = new FrmViewSheetCreator(doc, data, overallName, parentName, version);
            using (frmSC)
            {
                frmSC.ShowDialog();
                if (frmSC.DialogResult == System.Windows.Forms.DialogResult.Cancel)
                    return false;
            }
            #endregion

            #region Process Creations and Placements
            // List views to place in sheet center
            List<VSC_CenterView> centerView = new List<VSC_CenterView>();

            if (frmSC.ViewSheetCreationItems.Count == 0)
                return false;

            using (var trans = new Transaction(doc, "Create Views and Sheets"))
            {
                trans.Start();

                int overallViewCount = 0;
                int parentViewCount = 0;
                int dependentViewCount = 0;
                int sheetCount = 0;

                var overallOrParents = frmSC.ViewSheetCreationItems.Where(x => x.IsDependentView == false).ToList();
                var dependents = frmSC.ViewSheetCreationItems.Where(x => x.IsDependentView == true).ToList();
                string progressMessage = "{0} of " + overallOrParents.Count.ToString() + " views processed...";
                using (BecaProgressForm pf = new BecaProgressForm("Creating and Placing Overall/Parent items", progressMessage, overallOrParents.Count))
                {
                    // Create OVERALL Views
                    foreach (var item in overallOrParents)
                    {
                        // Primary and Secondary View Template set up
                        var primaryVT = data.ViewTemplates.Find(x => x.Name == item.ViewTemplateName).View;
                        View secondaryVT = null;
                        if (item.SecondaryViewTemplateName != "")
                        {
                            secondaryVT = data.ViewTemplates.Find(x => x.Name == item.SecondaryViewTemplateName).View;
                            VSC_Utilities.NotIncludeVTParameter(secondaryVT, scaleParameter, secondaryVTParameter); // Uncheck Include View Scale & Beca_SecondaryViewTemplate parameter
                        }

                        // Create PARENT or OVERALL View
                        var view = VSC_Utilities.CreateView(doc, item.ViewFamilyType.Id, item.LevelId, item.ViewName);
                        // Set View Templates
                        view.get_Parameter(BuiltInParameter.VIEW_TEMPLATE).Set(primaryVT.Id);

                        // Set Secondary VT, Scope Box and Scale
                        if (secondaryVT != null)
                            view.ApplyViewTemplateParameters(secondaryVT);
                        if (view.LookupParameter(secondaryVTParameter) != null)
                            view.LookupParameter(secondaryVTParameter).Set(item.SecondaryViewTemplateName);
                        if (item.ScopeBox != null)
                            view.get_Parameter(BuiltInParameter.VIEWER_VOLUME_OF_INTEREST_CROP).Set(item.ScopeBox.Id);
                        if (item.Scale != null)
                            view.Scale = (int)item.Scale;

                        if (item.OverallParentDependent == overallName)
                            overallViewCount++;

                        // Create PARENT View and assign ParentView property to ViewSheetCreationItems where IsDependent is true
                        if (item.OverallParentDependent == parentName)
                        {
                            frmSC.ViewSheetCreationItems.Where(x => x.OverallParentDependent == "Dependent" && x.BaseViewName == item.BaseViewName).ToList().ForEach(y => y.ParentView = view);
                            parentViewCount++;
                        }


                        if (item.SheetNumber != "-")
                        {
                            // Create Sheet
                            var sheet = VSC_Utilities.CreateSheet(doc, item.TitleBlockId, item.SheetName, item.SheetNumber);
                            // Set Sheet Parameters
                            VSC_Utilities.SetBecaSheetParameters(sheet, item);

                            // Optimize viewport creation with view isolation
                            var vp = VSC_Utilities.CreateOptimizedViewport(doc, sheet.Id, view.Id, new XYZ(0, 0, 0));
                            if (vp != null)
                            {
                                var titleblock = doc.GetElement(item.TitleBlockId);
                                var bb = titleblock.get_BoundingBox(sheet);
                                var titleblockCenter = bb.Min.Add(bb.Max).Multiply(0.5);
                                centerView.Add(new VSC_CenterView { View = vp, Sheet = sheet, Position = titleblockCenter });
                                sheetCount++;
                            }
                        }

                        pf.Increment();
                    }
                }
                progressMessage = "{0} of " + dependents.Count.ToString() + " views processed...";
                using (BecaProgressForm pf = new BecaProgressForm("Creating and Placing Dependent items", progressMessage, dependents.Count))
                {
                    // Create DEPENDENT Views
                    foreach (var item in dependents)
                    {
                        // Create dependent view
                        var dependentViewId = item.ParentView.Duplicate(ViewDuplicateOption.AsDependent);
                        var dependentView = doc.GetElement(dependentViewId) as ViewPlan;
                        dependentView.Name = item.DependentViewName;
                        if (item.ScopeBox != null)
                            dependentView.get_Parameter(BuiltInParameter.VIEWER_VOLUME_OF_INTEREST_CROP).Set(item.ScopeBox.Id);

                        dependentViewCount++;

                        // Create Sheet
                        var sheet = VSC_Utilities.CreateSheet(doc, item.TitleBlockId, item.SheetName, item.SheetNumber);
                        // Set Sheet Parameters
                        VSC_Utilities.SetBecaSheetParameters(sheet, item);

                        // Optimize viewport creation with view isolation
                        var vp = VSC_Utilities.CreateOptimizedViewport(doc, sheet.Id, dependentView.Id, new XYZ(0, 0, 0));
                        if (vp != null)
                        {
                            var titleblock = doc.GetElement(item.TitleBlockId);
                            var bb = titleblock.get_BoundingBox(sheet);
                            var titleblockCenter = bb.Min.Add(bb.Max).Multiply(0.5);
                            centerView.Add(new VSC_CenterView { View = vp, Sheet = sheet, Position = titleblockCenter });
                            sheetCount++;
                        }

                        pf.Increment();
                    }

                }
                TaskDialog.Show("View Sheet Creation", "Successfully creates:\n" + overallViewCount + " Overall views\n" + parentViewCount + " Parent views\n" + dependentViewCount + " Dependent views\n" + sheetCount + " sheets");

                trans.Commit();

            }

            using (var trans = new Transaction(doc, "Center View in Sheets"))
            {
                trans.Start();
                foreach (var item in centerView)
                {
                    item.View.SetBoxCenter(item.Position);
                    VSC_Utilities.SetBecaTitleblockParameters(doc, item.Sheet);
                }
                trans.Commit();
            }

            return true;
            #endregion

        }




    }
}
