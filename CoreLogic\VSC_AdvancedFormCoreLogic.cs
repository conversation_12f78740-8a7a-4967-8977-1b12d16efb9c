using Autodesk.Revit.ApplicationServices;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using Common.UI.Forms;
using MEP.SheetCreator.UI.Forms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Application = Autodesk.Revit.ApplicationServices.Application;
using TaskDialog = Autodesk.Revit.UI.TaskDialog;
using View = Autodesk.Revit.DB.View;


namespace MEP.SheetCreator.CoreLogic
{
    public static class VSC_AdvancedFormCoreLogic
    {
        public static bool RunLogic(Application app, Document doc)
        {
            #region Data Preparation and Form
            var version = app.VersionNumber;
            var overallName = "OVERALL";
            var parentName = "PARENT";
            var scaleParameter = "View Scale";
            var secondaryVTParameter = "Beca_SecondaryViewTemplate";
            var data = new VSC_Data(doc);

            if (data.ScopeBoxes.Count() == 0)
            {
                TaskDialog.Show("Null Scope Box", "No Scope Box found.\nPlease create Overall and Zone Scope Boxes.");
                return false;
            }
            if (data.ViewTemplates.Count() == 0)
            {
                TaskDialog.Show("Null View Template", "No View Template found.\nThis tool will not work without View Template.");
                return false;
            }

            var frmSC = new FrmViewSheetCreator(doc, data, overallName, parentName, version);
            using (frmSC)
            {
                frmSC.ShowDialog();
                if (frmSC.DialogResult == System.Windows.Forms.DialogResult.Cancel)
                    return false;
            }
            #endregion

            #region Process Creations and Placements
            // List views to place in sheet center
            List<VSC_CenterView> centerView = new List<VSC_CenterView>();

            if (frmSC.ViewSheetCreationItems.Count == 0)
                return false;

            using (var trans = new Transaction(doc, "Create Views and Sheets"))
            {
                trans.Start();

                int overallViewCount = 0;
                int parentViewCount = 0;
                int dependentViewCount = 0;
                int sheetCount = 0;

                var overallOrParents = frmSC.ViewSheetCreationItems.Where(x => x.IsDependentView == false).ToList();
                var dependents = frmSC.ViewSheetCreationItems.Where(x => x.IsDependentView == true).ToList();
                string progressMessage = "{0} of " + overallOrParents.Count.ToString() + " views processed...";
                using (BecaProgressForm pf = new BecaProgressForm("Creating and Placing Overall/Parent items", progressMessage, overallOrParents.Count))
                {
                    // Create OVERALL Views
                    foreach (var item in overallOrParents)
                    {
                        // Primary and Secondary View Template set up
                        var primaryVT = data.ViewTemplates.Find(x => x.Name == item.ViewTemplateName).View;
                        View secondaryVT = null;
                        if (item.SecondaryViewTemplateName != "")
                        {
                            secondaryVT = data.ViewTemplates.Find(x => x.Name == item.SecondaryViewTemplateName).View;
                            VSC_Utilities.NotIncludeVTParameter(secondaryVT, scaleParameter, secondaryVTParameter); // Uncheck Include View Scale & Beca_SecondaryViewTemplate parameter
                        }

                        // Create PARENT or OVERALL View
                        var view = VSC_Utilities.CreateView(doc, item.ViewFamilyType.Id, item.LevelId, item.ViewName);
                        // Set View Templates
                        view.get_Parameter(BuiltInParameter.VIEW_TEMPLATE).Set(primaryVT.Id);

                        // Set Secondary VT, Scope Box and Scale
                        if (secondaryVT != null)
                            view.ApplyViewTemplateParameters(secondaryVT);
                        if (view.LookupParameter(secondaryVTParameter) != null)
                            view.LookupParameter(secondaryVTParameter).Set(item.SecondaryViewTemplateName);
                        if (item.ScopeBox != null)
                            view.get_Parameter(BuiltInParameter.VIEWER_VOLUME_OF_INTEREST_CROP).Set(item.ScopeBox.Id);
                        if (item.Scale != null)
                            view.Scale = (int)item.Scale;

                        if (item.OverallParentDependent == overallName)
                            overallViewCount++;

                        // Create PARENT View and assign ParentView property to ViewSheetCreationItems where IsDependent is true
                        if (item.OverallParentDependent == parentName)
                        {
                            frmSC.ViewSheetCreationItems.Where(x => x.OverallParentDependent == "Dependent" && x.BaseViewName == item.BaseViewName).ToList().ForEach(y => y.ParentView = view);
                            parentViewCount++;
                        }


                        if (item.SheetNumber != "-")
                        {
                            // Create Sheet
                            var sheet = VSC_Utilities.CreateSheet(doc, item.TitleBlockId, item.SheetName, item.SheetNumber);
                            // Set Sheet Parameters
                            VSC_Utilities.SetBecaSheetParameters(sheet, item);

                            // Optimize viewport creation with view isolation
                            var vp = CreateOptimizedViewport(doc, sheet, view, item.TitleBlockId);
                            if (vp != null)
                            {
                                var titleblock = doc.GetElement(item.TitleBlockId);
                                var bb = titleblock.get_BoundingBox(sheet);
                                var titleblockCenter = bb.Min.Add(bb.Max).Multiply(0.5);
                                centerView.Add(new VSC_CenterView { View = vp, Sheet = sheet, Position = titleblockCenter });
                                sheetCount++;
                            }
                        }

                        pf.Increment();
                    }
                }
                progressMessage = "{0} of " + dependents.Count.ToString() + " views processed...";
                using (BecaProgressForm pf = new BecaProgressForm("Creating and Placing Dependent items", progressMessage, dependents.Count))
                {
                    // Create DEPENDENT Views
                    foreach (var item in dependents)
                    {
                        // Create dependent view
                        var dependentViewId = item.ParentView.Duplicate(ViewDuplicateOption.AsDependent);
                        var dependentView = doc.GetElement(dependentViewId) as ViewPlan;
                        dependentView.Name = item.DependentViewName;
                        if (item.ScopeBox != null)
                            dependentView.get_Parameter(BuiltInParameter.VIEWER_VOLUME_OF_INTEREST_CROP).Set(item.ScopeBox.Id);

                        dependentViewCount++;

                        // Create Sheet
                        var sheet = VSC_Utilities.CreateSheet(doc, item.TitleBlockId, item.SheetName, item.SheetNumber);
                        // Set Sheet Parameters
                        VSC_Utilities.SetBecaSheetParameters(sheet, item);

                        // Optimize viewport creation with view isolation
                        var vp = CreateOptimizedViewport(doc, sheet, dependentView, item.TitleBlockId);
                        if (vp != null)
                        {
                            var titleblock = doc.GetElement(item.TitleBlockId);
                            var bb = titleblock.get_BoundingBox(sheet);
                            var titleblockCenter = bb.Min.Add(bb.Max).Multiply(0.5);
                            centerView.Add(new VSC_CenterView { View = vp, Sheet = sheet, Position = titleblockCenter });
                            sheetCount++;
                        }

                        pf.Increment();
                    }

                }
                TaskDialog.Show("View Sheet Creation", "Successfully creates:\n" + overallViewCount + " Overall views\n" + parentViewCount + " Parent views\n" + dependentViewCount + " Dependent views\n" + sheetCount + " sheets");

                trans.Commit();

            }

            using (var trans = new Transaction(doc, "Center View in Sheets"))
            {
                trans.Start();
                foreach (var item in centerView)
                {
                    item.View.SetBoxCenter(item.Position);
                    VSC_Utilities.SetBecaTitleblockParameters(doc, item.Sheet);
                }
                trans.Commit();
            }

            return true;
            #endregion

        }

        /// <summary>
        /// Creates a viewport with optimized performance by temporarily isolating view elements
        /// to reduce regeneration time during viewport creation.
        /// </summary>
        /// <param name="doc">The document</param>
        /// <param name="sheet">The target sheet</param>
        /// <param name="view">The view to place</param>
        /// <param name="titleBlockId">The title block element ID</param>
        /// <returns>The created viewport, or null if creation failed</returns>
        private static Viewport CreateOptimizedViewport(Document doc, ViewSheet sheet, View view, ElementId titleBlockId)
        {
            Viewport viewport = null;

            try
            {
                // Check if the view has elements that can be isolated for performance
                var collector = new FilteredElementCollector(doc, view.Id)
                    .WhereElementIsNotElementType()
                    .ToElementIds();

                // If the view has many elements (threshold for optimization), use isolation technique
                if (collector.Count > 100) // Adjust threshold as needed
                {
                    // Use transaction group to ensure all temporary changes are rolled back
                    using (TransactionGroup txg = new TransactionGroup(doc))
                    {
                        txg.Start("Optimized Viewport Creation");

                        // Get elements in the current scope box (if any) for isolation
                        var elementsToIsolate = GetElementsInScopeBox(doc, view);

                        if (elementsToIsolate.Count > 0 && elementsToIsolate.Count < collector.Count)
                        {
                            // Temporarily isolate only relevant elements
                            using (Transaction tx = new Transaction(doc))
                            {
                                tx.Start("Isolate Elements for Viewport Creation");

                                // Isolate elements temporarily to reduce regeneration load
                                view.IsolateElementsTemporary(elementsToIsolate);

                                tx.Commit();
                            }

                            // Create viewport with isolated view (faster regeneration)
                            viewport = Viewport.Create(doc, sheet.Id, view.Id, new XYZ(0, 0, 0));

                            // Disable temporary isolation
                            using (Transaction tx = new Transaction(doc))
                            {
                                tx.Start("Disable Temporary Isolation");

                                if (view.IsTemporaryHideIsolateActive())
                                {
                                    view.DisableTemporaryViewMode(TemporaryViewMode.TemporaryHideIsolate);
                                }

                                tx.Commit();
                            }
                        }
                        else
                        {
                            // Fallback to standard creation if isolation doesn't help
                            viewport = Viewport.Create(doc, sheet.Id, view.Id, new XYZ(0, 0, 0));
                        }

                        // Don't commit the transaction group - this rolls back all temporary changes
                        // but keeps the viewport creation which was done outside the group's scope
                    }
                }
                else
                {
                    // For views with fewer elements, use standard creation
                    viewport = Viewport.Create(doc, sheet.Id, view.Id, new XYZ(0, 0, 0));
                }
            }
            catch (Exception ex)
            {
                // Log error and fallback to standard creation
                TaskDialog.Show("Viewport Creation Warning",
                    $"Optimized viewport creation failed: {ex.Message}\nFalling back to standard creation.");

                try
                {
                    viewport = Viewport.Create(doc, sheet.Id, view.Id, new XYZ(0, 0, 0));
                }
                catch
                {
                    // If even standard creation fails, return null
                    viewport = null;
                }
            }

            return viewport;
        }

        /// <summary>
        /// Gets elements within the view's scope box for isolation purposes
        /// </summary>
        /// <param name="doc">The document</param>
        /// <param name="view">The view to analyze</param>
        /// <returns>Collection of element IDs within the scope box</returns>
        private static ICollection<ElementId> GetElementsInScopeBox(Document doc, View view)
        {
            var elementsInScope = new List<ElementId>();

            try
            {
                // Get the scope box parameter
                var scopeBoxParam = view.get_Parameter(BuiltInParameter.VIEWER_VOLUME_OF_INTEREST_CROP);
                if (scopeBoxParam != null && scopeBoxParam.AsElementId() != ElementId.InvalidElementId)
                {
                    var scopeBoxId = scopeBoxParam.AsElementId();
                    var scopeBox = doc.GetElement(scopeBoxId) as Element;

                    if (scopeBox != null)
                    {
                        // Get elements that intersect with the scope box
                        var collector = new FilteredElementCollector(doc, view.Id)
                            .WhereElementIsNotElementType();

                        // Filter elements by categories that are typically heavy for regeneration
                        var heavyCategories = new List<BuiltInCategory>
                        {
                            BuiltInCategory.OST_Walls,
                            BuiltInCategory.OST_Floors,
                            BuiltInCategory.OST_Roofs,
                            BuiltInCategory.OST_Ceilings,
                            BuiltInCategory.OST_Columns,
                            BuiltInCategory.OST_StructuralFraming,
                            BuiltInCategory.OST_MEPCurves,
                            BuiltInCategory.OST_DuctCurves,
                            BuiltInCategory.OST_PipeCurves
                        };

                        foreach (var category in heavyCategories)
                        {
                            try
                            {
                                var categoryElements = collector
                                    .OfCategory(category)
                                    .ToElementIds();

                                elementsInScope.AddRange(categoryElements);
                            }
                            catch
                            {
                                // Skip categories that don't exist in this view
                                continue;
                            }
                        }
                    }
                }

                // If no scope box or scope box filtering failed, get a subset of elements
                if (elementsInScope.Count == 0)
                {
                    var allElements = new FilteredElementCollector(doc, view.Id)
                        .WhereElementIsNotElementType()
                        .ToElementIds();

                    // Take a reasonable subset for isolation (e.g., first 50% of elements)
                    elementsInScope = allElements.Take(Math.Max(1, allElements.Count / 2)).ToList();
                }
            }
            catch
            {
                // Return empty collection if anything fails
                elementsInScope.Clear();
            }

            return elementsInScope;
        }
    }
}
