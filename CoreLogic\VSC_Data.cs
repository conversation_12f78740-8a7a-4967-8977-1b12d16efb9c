﻿using Autodesk.Revit.DB;
using BecaRevitUtilities.Collectors;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using View = Autodesk.Revit.DB.View;


namespace MEP.SheetCreator.CoreLogic
{
    public class VSC_Data
    {

        #region Fields
        public List<VSC_Level> Levels;
        public List<VSC_ViewTemplate> ViewTemplates;
        public List<VSC_ScopeBox> ScopeBoxes;
        public List<FamilySymbol> TitleBlocks;
        public List<View> Legends;
        public List<VSC_Discipline> Disciplines;
        public List<string> SheetNumbers;
        public List<string> ViewNames;
        public List<string> Scales;
        #endregion

        #region Properties



        #endregion

        #region Constructors
        public VSC_Data(Document doc)
        {
            GetLevels(doc);
            GetViewTemplates(doc);
            GetScopeBoxes(doc);
            TitleBlocks = GetTitleBlocks(doc);
            Legends = GetLegends(doc);
            SheetNumbers = GetSheetNumbers(doc);
            ViewNames = GetViewNames(doc);
            Scales = ScaleList();

            AssignDisciplines();

        }


        #endregion

        #region Methods
        private List<string> GetSheetNumbers(Document doc)
        {
            return new FilteredElementCollector(doc).WhereElementIsNotElementType().OfClass(typeof(ViewSheet))
                .Cast<ViewSheet>().Where(x => x.IsPlaceholder == false).Select(y => y.SheetNumber).ToList();
        }

        private List<string> GetViewNames(Document doc)
        {
            return new FilteredElementCollector(doc).OfClass(typeof(View)).Cast<View>()
                .Where(x => !x.IsTemplate).OrderBy(v => v.Name).Select(y => y.Name).ToList();
        }

        private void GetLevels(Document doc)
        {
            var levelElements = new FilteredElementCollector(doc).OfClass(typeof(Level)).ToElements();
            var sortLevelElements = from Level l in levelElements orderby l.Elevation ascending select l;

            Levels = new List<VSC_Level>();
            foreach (var level in sortLevelElements)
            {
                Levels.Add(new VSC_Level(level));
            }
        }

        private void GetViewTemplates(Document doc)
        {
            var viewTemplates = new FilteredElementCollector(doc).OfClass(typeof(View)).Cast<View>().Where(x => x.IsTemplate).ToList();
            var sortViewElements = from View v in viewTemplates orderby v.Name ascending select v;

            ViewTemplates = new List<VSC_ViewTemplate>();
            foreach (var view in sortViewElements)
            {
                ViewTemplates.Add(new VSC_ViewTemplate(view));
            }
        }

        private void GetScopeBoxes(Document doc)
        {
            var scopeBoxes = new FilteredElementCollector(doc).OfCategory(BuiltInCategory.OST_VolumeOfInterest).ToElements().ToList();
            ScopeBoxes = new List<VSC_ScopeBox>();
            foreach (var scopeBox in scopeBoxes)
            {
                ScopeBoxes.Add(new VSC_ScopeBox(scopeBox));
            }

        }

        private List<FamilySymbol> GetTitleBlocks(Document doc)
        {
            return ElementCollectorUtility.GetAllTitleBlocks(doc).Select(x => x as FamilySymbol).ToList();
        }

        private List<View> GetLegends(Document doc)
        {
            return new FilteredElementCollector(doc).OfClass(typeof(View)).Cast<View>()
                .Where(x => !x.Name.Contains("<Revision Schedule>")).Where(y => y.ViewType == ViewType.Legend).ToList();
        }

        private void AssignDisciplines()
        {
            Disciplines = new List<VSC_Discipline>();

            Disciplines.Add(new VSC_Discipline("", "SITE PLAN", 1, ""));
            Disciplines.Add(new VSC_Discipline("", "DEMOLITION", 1, ""));
            Disciplines.Add(new VSC_Discipline("", "TEMPORARY WORKS", 1, ""));

            Disciplines.Add(new VSC_Discipline("BB", "FLOOR PLAN", 2, "COMBINED SERVICES"));
            Disciplines.Add(new VSC_Discipline("BB", "REFLECTED CEILING LAYOUT", 2, "COMBINED SERVICES"));
            Disciplines.Add(new VSC_Discipline("BB", "COMBINED SERVICES LAYOUT", 3, "COMBINED SERVICES"));
            Disciplines.Add(new VSC_Discipline("BB", "COMBINED SERVICES DETAIL LAYOUT", 7, "COMBINED SERVICES"));

            Disciplines.Add(new VSC_Discipline("BC", "COMMUNICATION LAYOUT", 3, "COMMUNICATION"));
            Disciplines.Add(new VSC_Discipline("BC", "COMMS DETAIL LAYOUT", 7, "COMMUNICATION"));

            Disciplines.Add(new VSC_Discipline("BE", "POWER LAYOUT", 2, "ELECTRICAL SERVICES"));
            Disciplines.Add(new VSC_Discipline("BE", "LIGHTING LAYOUT", 3, "ELECTRICAL SERVICES"));
            Disciplines.Add(new VSC_Discipline("BE", "CABLE CONTAINMENT LAYOUT", 4, "ELECTRICAL SERVICES"));
            Disciplines.Add(new VSC_Discipline("BE", "EMERGENCY LIGHTING LAYOUT", 5, "ELECTRICAL SERVICES"));
            Disciplines.Add(new VSC_Discipline("BE", "ELECTRICAL DETAIL LAYOUT", 7, "ELECTRICAL SERVICES"));

            Disciplines.Add(new VSC_Discipline("BF", "FIRE PROTECTION LAYOUT", 2, "FIRE PROTECTION"));
            Disciplines.Add(new VSC_Discipline("BF", "FIRE ALARM LAYOUT", 3, "FIRE PROTECTION"));
            Disciplines.Add(new VSC_Discipline("BF", "SPRINKLER LAYOUT", 4, "FIRE PROTECTION"));
            Disciplines.Add(new VSC_Discipline("BF", "FIRE ALARM DETAIL LAYOUT", 7, "FIRE PROTECTION"));

            Disciplines.Add(new VSC_Discipline("BG", "MEDICAL GASES LAYOUT", 2, "MEDICAL/LABORATORY GASES"));
            Disciplines.Add(new VSC_Discipline("BG", "LAB GASES LAYOUT", 3, "MEDICAL/LABORATORY GASES"));

            Disciplines.Add(new VSC_Discipline("BH", "HVAC DUCTWORK LAYOUT", 2, "HVAC SERVICES"));
            Disciplines.Add(new VSC_Discipline("BH", "HVAC LAYOUT", 2, "HVAC SERVICES"));
            Disciplines.Add(new VSC_Discipline("BH", "HVAC PIPEWORK LAYOUT", 3, "HVAC SERVICES"));
            Disciplines.Add(new VSC_Discipline("BH", "HVAC DETAIL LAYOUT", 7, "HVAC SERVICES"));

            Disciplines.Add(new VSC_Discipline("BL", "VERTICAL TRANSPORTATION LAYOUT", 2, "VERTICAL TRANSPORTATION"));

            Disciplines.Add(new VSC_Discipline("BN", "NURSE CALL LAYOUT", 2, "NURSE CALL"));

            Disciplines.Add(new VSC_Discipline("BP", "PLUMBING LAYOUT", 2, "PLUMBING & DRAINAGE"));
            Disciplines.Add(new VSC_Discipline("BP", "DRAINAGE LAYOUT", 3, "PLUMBING & DRAINAGE"));
            Disciplines.Add(new VSC_Discipline("BP", "GAS LAYOUT", 4, "PLUMBING & DRAINAGE"));
            Disciplines.Add(new VSC_Discipline("BP", "RAINWATER LAYOUT", 5, "PLUMBING & DRAINAGE"));
            Disciplines.Add(new VSC_Discipline("BP", "PLUMBING & DRAINAGE DETAIL LAYOUT", 7, "PLUMBING & DRAINAGE"));

            Disciplines.Add(new VSC_Discipline("BS", "SECURITY LAYOUT", 2, "SECURITY"));
            Disciplines.Add(new VSC_Discipline("BS", "CCTV LAYOUT", 3, "SECURITY"));
            Disciplines.Add(new VSC_Discipline("BS", "SECURITY DETAIL LAYOUT", 7, "SECURITY"));
            Disciplines.Add(new VSC_Discipline("BS", "CCTV CAMERA VIEWS", 8, "SECURITY"));

            Disciplines.Add(new VSC_Discipline("BV", "AV LAYOUT", 2, "AUDIO VISUAL"));

            Disciplines.Add(new VSC_Discipline("BW", "POOL WATER SERVICES LAYOUT", 2, "POOL WATER SERVICES"));
        }

        private List<string> ScaleList()
        {
            return new List<string>{
                "1-1",
                "1-2",
                "1-5",
                "1-10",
                "1-20",
                "1-25",
                "1-50",
                "1-100",
                "1-200",
                "1-250",
                "1-400",
                "1-500",
                "1-1000",
                "1-2000",
                "1-5000"};
        }
        #endregion

    }
}