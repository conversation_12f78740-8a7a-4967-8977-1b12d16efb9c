﻿using Autodesk.Revit.DB;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.SheetCreator.CoreLogic
{
    public class VSC_Level
    {

        #region Fields
        public Level Level;
        public string LevelName { get; set; }
        public string InputString { get; set; }
        public string InputNumber { get; set; }

        #endregion

        #region Properties



        #endregion

        #region Constructors
        public VSC_Level(Level l)
        {
            Level = l;
            LevelName = l.Name;
            InputString = "";
            InputNumber = "";
        }


        #endregion

        #region Methods



        #endregion

    }
}