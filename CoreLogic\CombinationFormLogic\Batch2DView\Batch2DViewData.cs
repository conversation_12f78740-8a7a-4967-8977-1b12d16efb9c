﻿using Autodesk.Revit.DB;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using View = Autodesk.Revit.DB.View;

namespace MEP.SheetCreator.CoreLogic.CombinationFormLogic.Batch2DView
{
    public  class Batch2DViewData
    {
        public List<Level> Levels;
        public List<Element> Views;
        public IList<Element> FloorViewTemplates = new List<Element>();
        public IList<Element> RCPViewTemplates = new List<Element>();
        public List<Element> Scopeboxes;

        public Batch2DViewData(Document doc)
        {
            // Getting all the levels
            var levels = new FilteredElementCollector(doc).OfClass(typeof(Level)).ToElements();
            Levels = (from Level level in levels orderby level.Elevation ascending select level).ToList();

            // Getting All The Views
            var viewElements = new FilteredElementCollector(doc).OfClass(typeof(View)).ToElements();
            Views = (from Element rqv in viewElements orderby rqv.Name ascending select rqv).ToList();

            // Getting Floor View Templates and RCP View Templates
            foreach (View view in Views)
            {
                if (view.ViewType == ViewType.FloorPlan & view.IsTemplate == true)
                {
                    FloorViewTemplates.Add(view);
                }
                else if (view.ViewType == ViewType.CeilingPlan & view.IsTemplate == true)
                {
                    RCPViewTemplates.Add(view);
                }
            }

            // Geting All the scope boxes
            Scopeboxes = new FilteredElementCollector(doc).OfCategory(BuiltInCategory.OST_VolumeOfInterest).ToList();
        }
    }
}
