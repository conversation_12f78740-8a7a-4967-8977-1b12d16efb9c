﻿using Autodesk.Revit.DB;
using Common.Extensions;
using Common.UI.Forms;
using MEP.SheetCreator.CoreLogic;
using MEP.SheetCreator.UI.UIData;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Xml.Serialization;
using System.DirectoryServices.AccountManagement;

namespace MEP.SheetCreator.UI.Forms
{
    public partial class FrmViewSheetCreator : BecaBaseForm
    {

        #region Fields
        Document _Doc;
        VSC_Data _Data;
        public Dictionary<ElementId, string> TitleBlocksDict;

        DataTable _DataTable;
        int indexRow;

        public List<VSC_CreationItems> ViewSheetCreationItems;

        string OverallName;
        string ParentName;

        public bool CreateParent;

        List<string> Scales;

        string SelectedOverallScale;
        string SelectedParentScale;


        // For Serialization
        string VSC_Path;
        string XML_ScopeBoxFileName;
        string XML_LevelFileName;
        string XML_InfoFileName;
        string XML_TitleblockSettingFileName;
        DataTable DT_ScopeBox = new DataTable("ScopeBox");
        DataTable DT_Level = new DataTable("Level");
        DataTable DT_TitleblockSetting = new DataTable("TitleblockSetting");

        string UserName = Environment.UserName;
        string UpdateTime = DateTime.Now.ToString("MM/dd/yyyy hh:mm tt");
        string ComputerName = Environment.MachineName;
        string ProjectName;

        VSC_Info UpdateInfo;

        #endregion

        #region Properties



        #endregion

        #region Constructors

        public FrmViewSheetCreator(Document doc, VSC_Data data, string overall, string parent, string version)
        {
            InitializeComponent();

            // Path and file name settings for XML Serialization
            ProjectName = doc.Title;

            #region XML Serialization
            VSC_Path = Path.Combine(new string[] { @"P:\513\5130000\VSC", version, ProjectName });
            try
            {
                Directory.CreateDirectory(VSC_Path);
            }
            catch (Exception)
            {

                MessageBox.Show("Cannot access P drive", "Missing path");
            }

            XML_ScopeBoxFileName = Path.Combine(new string[] { VSC_Path, "VSC_ScopeBox.xml" });
            XML_LevelFileName = Path.Combine(new string[] { VSC_Path, "VSC_Level.xml" });
            XML_InfoFileName = Path.Combine(new string[] { VSC_Path, "VSC_Info.xml" });
            XML_TitleblockSettingFileName = Path.Combine(new string[] { VSC_Path, "VSC_Titleblock.xml" });

            if (File.Exists(XML_InfoFileName))
            {
                UpdateInfo = ReadInfoXML();
                lbl_ProjectName.Text = UpdateInfo.ProjectName;
                lbl_LastUpdatedBy.Text = UpdateInfo.LastUpdatedBy;
                lbl_UpdateTime.Text = UpdateInfo.UpdateTime;
                lbl_ComputerName.Text = UpdateInfo.UpdaterComputerName;
            }
            else
                MessageBox.Show("Cannot get last update info.\nPlease check connection to database.", "Connection issue");
            #endregion

            lbl_ProjectName.Text = ProjectName;
            lbl_RevitVersion.Text = version;

            _Doc = doc;
            _Data = data;
            OverallName = overall;
            ParentName = parent;

            PopulateLevelList(data);
            PopulateScopeBoxList(data);
            PopulateTitleBlockList(data);
            Populate2ndVTViewType(data);
            for (int i = 0; i < cb_01_2ndViewType.Items.Count; i++)
            {
                if (cb_01_2ndViewType.GetItemText(cb_01_2ndViewType.Items[i]) == "FloorPlan")
                    cb_01_2ndViewType.SelectedIndex = i;
            }

            lb_01_2ndVT.SelectedIndex = 0;

            PopulateDrawingTypeList();

            InitialiseADGV(data);
            Initialize_dgv_02_ViewsToBePlaced();

            ViewSheetCreationItems = new List<VSC_CreationItems>();

            CreateParent = false;

            Scales = data.Scales;

            foreach (var s in Scales)
            {
                cb_01_OverallViewScale.Items.Add(s);
                cb_01_ParentViewScale.Items.Add(s);
            }
            cb_01_OverallViewScale.SelectedIndex = 8;
            SelectedOverallScale = cb_01_OverallViewScale.SelectedItem.ToString();
            cb_01_ParentViewScale.SelectedIndex = 7;
            SelectedParentScale = cb_01_ParentViewScale.SelectedItem.ToString();

            SetTitleblockSettingList();

        }

        #endregion

        #region Methods
        private void WriteStartInfoXML()
        {
            var vsc_StartInfo = new VSC_Info
            {
                StartBy = UserPrincipal.Current.UserPrincipalName,
                StartTime = DateTime.Now.ToString("dd/MM/yyyy hh:mm tt")
            };
            var writer = new XmlSerializer(typeof(VSC_Info));
            var file = new StreamWriter(XML_InfoFileName);
            writer.Serialize(file, vsc_StartInfo);
            file.Close();
        }

        private void WriteInfoXML()
        {
            var vsc_Info = new VSC_Info
            {
                LastUpdatedBy = UserPrincipal.Current.UserPrincipalName,
                UpdateTime = DateTime.Now.ToString("dd/MM/yyyy hh:mm tt"),
                UpdaterComputerName = ComputerName,
                ProjectName = ProjectName
            };
            var writer = new XmlSerializer(typeof(VSC_Info));
            var file = new StreamWriter(XML_InfoFileName);
            writer.Serialize(file, vsc_Info);
            file.Close();
        }

        private VSC_Info ReadInfoXML()
        {
            var reader = new XmlSerializer(typeof(VSC_Info));
            var file = new StreamReader(XML_InfoFileName);
            var vsc_Info = (VSC_Info)reader.Deserialize(file);
            file.Close();

            return vsc_Info;
        }

        private void InitialiseADGV(VSC_Data data)
        {
            bs_01_ViewTemplates.DataSource = data.ViewTemplates.ToDataTable();
            VSC_AdgvColumnsBinding.ViewTemplateData(adgv_01_AvailableVT, bs_01_ViewTemplates);
            adgv_01_AvailableVT.BackgroundColor = System.Drawing.Color.White;
            adgv_01_AvailableVT.RowHeadersVisible = false;
            adgv_01_AvailableVT.DefaultCellStyle.Font = new System.Drawing.Font("Arial", 9);
            adgv_01_AvailableVT.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;

        }

        private void PopulateLevelList(VSC_Data data)
        {
            if (File.Exists(XML_LevelFileName))
            {
                // Load saved Level inputs
                DT_Level.ReadXml(XML_LevelFileName);
                dgv_01_Levels.DataSource = DT_Level;
                dgv_01_Levels.RowHeadersVisible = false;
                dgv_01_Levels.Columns["Level Name"].ReadOnly = true;
            }
            else
            {
                DT_Level.Columns.Add("Level Name");
                DT_Level.Columns.Add("Input String");
                DT_Level.Columns.Add("Input Number");

                foreach (var item in data.Levels)
                {
                    DT_Level.Rows.Add(item.LevelName, item.InputString, item.InputNumber);
                }

                dgv_01_Levels.DataSource = DT_Level;
                dgv_01_Levels.RowHeadersVisible = false;
                dgv_01_Levels.Columns["Level Name"].ReadOnly = true;
            }
        }

        private void PopulateScopeBoxList(VSC_Data data)
        {
            if (File.Exists(XML_ScopeBoxFileName))
            {
                // Load saved ScopeBox input
                DT_ScopeBox.ReadXml(XML_ScopeBoxFileName);

                dgv_01_ScopeBox.DataSource = DT_ScopeBox;
                dgv_01_ScopeBox.RowHeadersVisible = false;
                dgv_01_ScopeBox.Columns["Scope Box Name"].ReadOnly = true;

                int diff = data.ScopeBoxes.Count - dgv_01_ScopeBox.Rows.Count;

                for (int i = 0; i < data.ScopeBoxes.Count; i++)
                {
                    if (i < data.ScopeBoxes.Count - diff)
                        dgv_01_ScopeBox.Rows[i].Cells[0].Value = data.ScopeBoxes[i].ScopeBoxName;
                    else
                        DT_ScopeBox.Rows.Add(data.ScopeBoxes[i].ScopeBoxName, "", "");

                }
            }
            else
            {
                DT_ScopeBox.Columns.Add("Scope Box Name");
                DT_ScopeBox.Columns.Add("Input String");
                DT_ScopeBox.Columns.Add("Input Number");
                DT_ScopeBox.Columns.Add(OverallName, System.Type.GetType("System.Boolean"));

                foreach (var item in data.ScopeBoxes)
                {
                    DT_ScopeBox.Rows.Add(item.ScopeBoxName, item.InputName, item.InputNumber, false);
                }

                dgv_01_ScopeBox.DataSource = DT_ScopeBox;
                dgv_01_ScopeBox.RowHeadersVisible = false;
                dgv_01_ScopeBox.Columns["Scope Box Name"].ReadOnly = true;
            }
            

        }

        private void PopulateTitleBlockList(VSC_Data data)
        {
            lb_02_TitleBlocks.DataSource = data.TitleBlocks.Select(x => x.Name).ToList();
            
        }

        private void Populate2ndVTViewType(VSC_Data data)
        {
            foreach (var item in data.ViewTemplates.Select(x => x.View.ViewType).Distinct().ToList())
            {
                cb_01_2ndViewType.Items.Add(item);
            }
        }

        private void Initialize_dgv_02_ViewsToBePlaced()
        {
            var threeChar = new DataGridViewTextBoxColumn();
            threeChar.MaxInputLength = 3;
            threeChar.HeaderText = "Edit last 3 digits";
            threeChar.DefaultCellStyle.BackColor = System.Drawing.Color.LightGray;
            dgv_02_ViewsToBePlaced.Columns.Add(threeChar);

            _DataTable = new DataTable();
            _DataTable.Columns.Add("Sheet Number");
            _DataTable.Columns.Add("Sheet Name");
            _DataTable.Columns.Add("View Name");
            _DataTable.Columns.Add("Primary View Template");
            _DataTable.Columns.Add("Secondary View Template");
            _DataTable.Columns.Add("Level");
            _DataTable.Columns.Add("View Type");
            _DataTable.Columns.Add("ScopeBox");
            _DataTable.Columns.Add("Scale");
            _DataTable.Columns.Add("DisciplineName");
            _DataTable.Columns.Add("View");
            _DataTable.Columns.Add("BaseViewName");
            _DataTable.Columns.Add("Designer");
            _DataTable.Columns.Add("DrawnBy");
            _DataTable.Columns.Add("Date");

            dgv_02_ViewsToBePlaced.DataSource = _DataTable;
            dgv_02_ViewsToBePlaced.RowHeadersVisible = false;

            dgv_02_ViewsToBePlaced.Columns["Sheet Number"].ReadOnly = true;
            dgv_02_ViewsToBePlaced.Columns["Sheet Name"].ReadOnly = true;
            dgv_02_ViewsToBePlaced.Columns["View Name"].ReadOnly = true;
            dgv_02_ViewsToBePlaced.Columns["Primary View Template"].ReadOnly = true;
            dgv_02_ViewsToBePlaced.Columns["Secondary View Template"].ReadOnly = true;
            dgv_02_ViewsToBePlaced.Columns["Level"].ReadOnly = true;
            dgv_02_ViewsToBePlaced.Columns["View Type"].ReadOnly = true;
            dgv_02_ViewsToBePlaced.Columns["ScopeBox"].ReadOnly = true;
            dgv_02_ViewsToBePlaced.Columns["Scale"].ReadOnly = true;

            dgv_02_ViewsToBePlaced.Columns["DisciplineName"].Visible = false;
            dgv_02_ViewsToBePlaced.Columns["BaseViewName"].Visible = false;
        }

        private void ClearDgv(DataGridView dgv)
        {
            var dt = (DataTable)dgv.DataSource;
            if (dt != null)
                dt.Clear();
        }

        #region UI

        #region UI Helpers

        #endregion

        #region Button Clicks



        #endregion

        #endregion

        #endregion
        private void PopulateDrawingTypeList()
        {
            var dataTable = new DataTable();
            dataTable.Columns.Add("Sequence Number");
            dataTable.Columns.Add("Discipline");
            dataTable.Columns.Add("Drawing Type");
            dataTable.Columns.Add("Custom Drawing Type");
            dataTable.Columns.Add("DisciplineName");

            foreach (var item in _Data.Disciplines.OrderBy(x => x.SequenceNumber))
            { 
                dataTable.Rows.Add(item.SequenceNumber.ToString(), item.Name, item.DrawingType, item.CustomInput, item.LongName);
            }
            dgv_01_DrawingTypeList.DataSource = dataTable;
            dgv_01_DrawingTypeList.RowHeadersVisible = false;
            dgv_01_DrawingTypeList.Columns["Sequence Number"].ReadOnly = true;
            dgv_01_DrawingTypeList.Columns["Sequence Number"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv_01_DrawingTypeList.Columns["Drawing Type"].ReadOnly = true;
            dgv_01_DrawingTypeList.Rows[0].Selected = true;
            dgv_01_DrawingTypeList.Columns["DisciplineName"].Visible = false;

        }

        private void SetTitleblockSettingList()
        {
            if (File.Exists(XML_TitleblockSettingFileName))
            {
                // Load saved Titleblock settings
                DT_TitleblockSetting.ReadXml(XML_TitleblockSettingFileName);
                dgv_02_TitleblockSetting.DataSource = DT_TitleblockSetting;
                dgv_02_TitleblockSetting.RowHeadersVisible = false;
                dgv_02_TitleblockSetting.Columns["Discipline"].ReadOnly = true;
                dgv_02_TitleblockSetting.Columns["Date"].ReadOnly = true;
            }
            else
            {
                DT_TitleblockSetting.Columns.Add("Discipline");
                DT_TitleblockSetting.Columns.Add("Designer");
                DT_TitleblockSetting.Columns.Add("Drawn By");
                DT_TitleblockSetting.Columns.Add("Date");
#if TargetYear2025 || TargetYear2026
                foreach (var item in Enumerable.DistinctBy(_Data.Disciplines, x => x.Name).Select(y => y.Name))
                {
                    if (item != "")
                        DT_TitleblockSetting.Rows.Add(
                            item,
                            "",
                            "",
                            "");
                }
#else
                foreach (var item in _Data.Disciplines.DistinctBy( x => x.Name).Select(y => y.Name))
                {
                    if (item != "")
                        DT_TitleblockSetting.Rows.Add(
                            item,
                            "",
                            "",
                            "");
                }
#endif

                dgv_02_TitleblockSetting.DataSource = DT_TitleblockSetting;
                dgv_02_TitleblockSetting.RowHeadersVisible = false;
                dgv_02_TitleblockSetting.Columns["Discipline"].ReadOnly = true;
                dgv_02_TitleblockSetting.Columns["Date"].ReadOnly = true;
            }

        }

        private void cb_01_2ndViewType_SelectedIndexChanged(object sender, EventArgs e)
        {
            lb_01_2ndVT.Items.Clear();
            foreach (var item in _Data.ViewTemplates)
            {
                if (cb_01_2ndViewType.SelectedItem.ToString() == "ALL")
                    lb_01_2ndVT.Items.Add(item.Name);
                else if (item.ViewType == cb_01_2ndViewType.SelectedItem.ToString())
                    lb_01_2ndVT.Items.Add(item.Name);
            }
        }

        private void btn_01_AssignDrawingType_Click(object sender, EventArgs e)
        {
            if (dgv_01_DrawingTypeList.SelectedRows[0].Cells[1].Value.ToString() == "")
            {
                MessageBox.Show("Please input Discipline Name");
                this.DialogResult = DialogResult.None;
            }
            else
            {
                foreach (DataGridViewCell item in adgv_01_AvailableVT.SelectedCells)
                {
                    if (item.ColumnIndex == 2) 
                    {
                        if (dgv_01_DrawingTypeList.SelectedCells[3].Value.ToString() == "") // Format: BB.1.LIGHTING LAYOUT
                            item.Value =
                                dgv_01_DrawingTypeList.SelectedCells[1].Value.ToString() + "-" +
                                dgv_01_DrawingTypeList.SelectedCells[0].Value.ToString() + "-" +
                                dgv_01_DrawingTypeList.SelectedCells[2].Value.ToString();
                        else
                            item.Value =
                                dgv_01_DrawingTypeList.SelectedCells[1].Value.ToString() + "-" +
                                dgv_01_DrawingTypeList.SelectedCells[0].Value.ToString() + "-" +
                                dgv_01_DrawingTypeList.SelectedCells[3].Value.ToString();
                    }

                    if (item.ColumnIndex == 4) // DisciplineName Column
                        item.Value = dgv_01_DrawingTypeList.SelectedCells[4].Value.ToString();
                }
            }
            
        }

        private void btn_01_Assign2ndVT_Click(object sender, EventArgs e)
        {
            int invalidViewType = 0;
            foreach (DataGridViewRow row in adgv_01_AvailableVT.SelectedRows)
            {
                if (row.Cells[0].Value.ToString() == cb_01_2ndViewType.SelectedItem.ToString())
                    row.Cells[3].Value = lb_01_2ndVT.SelectedItem.ToString();
                else
                    invalidViewType++;
            }
            if (invalidViewType > 0)
                MessageBox.Show(invalidViewType + " views cannot be applied with selected 2nd Template", "Invalid View Type");
        }

        private void btn_01_CreateViews_Click(object sender, EventArgs e)
        {
            // Checks inputs: Overall checked & selected? drawing type applied? 
            var overallCount = 0;
            var parentCount = 0;
            var checkDrawingTypeNotApplied = 0;
            foreach (DataGridViewRow row in dgv_01_ScopeBox.SelectedRows)
            {
                if (Convert.ToBoolean(row.Cells[3].Value))
                    overallCount++;
                else
                    parentCount++;
            }
            foreach (DataGridViewRow row in adgv_01_AvailableVT.SelectedRows)
            {
                if (row.Cells[2].Value.ToString() == "")
                    checkDrawingTypeNotApplied++;
            }


            if (overallCount > 0 && parentCount == 0)
                CreateParent = false;
            else
                CreateParent = true;

            if (overallCount == 0 || checkDrawingTypeNotApplied > 0)
            {
                if (overallCount == 0)
                {
                    MessageBox.Show("Select at lease one Overall View.", "Null Overall View");
                    this.DialogResult = DialogResult.None;
                }
                if (checkDrawingTypeNotApplied > 0)
                {
                    MessageBox.Show(checkDrawingTypeNotApplied + " views doesn't have Drawing Type applied", "Missing Drawing Type");
                    this.DialogResult = DialogResult.None;
                }
            }
            else // Input checks OK > Go!
            {
                foreach (DataGridViewRow rowLevel in dgv_01_Levels.SelectedRows)
                {
                    // This will get the selected Level
                    var editedLevelNameForSheetNumber = rowLevel.Cells[2].Value.ToString();
                    var editedLevelNameForViewAndSheetName = rowLevel.Cells[1].Value.ToString();

                    foreach (DataGridViewRow rowView in adgv_01_AvailableVT.SelectedRows)
                    {
                        // Get drawing type and sequence number
                        var drawingType = rowView.Cells[2].Value.ToString().Substring(5);
                        var sequenceNumber = rowView.Cells[2].Value.ToString()[3].ToString(); 
                        var selectedDiscipline = rowView.Cells[2].Value.ToString().Substring(0, 2);

                        // Scope Box and Dependent View
                        foreach (DataGridViewRow rowScopeBox in dgv_01_ScopeBox.SelectedRows)
                        {
                            var stringInput = rowScopeBox.Cells[1].Value.ToString();
                            var numberInput = rowScopeBox.Cells[2].Value.ToString();

                            if (Convert.ToBoolean(rowScopeBox.Cells[3].Value.ToString()))
                            {
                                // OVERALL View row in Tab 2
                                var overallSheetNumber = VSC_Utilities.ExtractSheetNumberFromVT(selectedDiscipline, sequenceNumber, editedLevelNameForSheetNumber, numberInput);
                                _DataTable.Rows.Add(
                                        overallSheetNumber,
                                        VSC_Utilities.ExtractSheetNameFromVT(tb_01_Prefix.Text, editedLevelNameForViewAndSheetName, drawingType, stringInput, tb_01_Suffix.Text),
                                        VSC_Utilities.ExtractViewNameFromVT(tb_01_Prefix.Text, editedLevelNameForViewAndSheetName, drawingType, stringInput, SelectedOverallScale, tb_01_Suffix.Text),
                                        rowView.Cells[1].Value.ToString(),
                                        rowView.Cells[3].Value.ToString(),
                                        rowLevel.Cells[0].Value.ToString(),
                                        rowView.Cells[0].Value.ToString(),
                                        rowScopeBox.Cells[0].Value.ToString(),
                                        SelectedOverallScale,
                                        rowView.Cells[4].Value.ToString(), // Cells[4] = DisciplineName
                                        OverallName, // Tag as Overall in Cell[11] = View 
                                        VSC_Utilities.ExtractBaseViewNameFromVT(tb_01_Prefix.Text, editedLevelNameForViewAndSheetName, drawingType) // BaseViewName
                                        ); 
                                // Create PARENT View row in Tab 2 if checked
                                if (CreateParent)
                                    _DataTable.Rows.Add(
                                    "-",
                                    "-",
                                    VSC_Utilities.ExtractViewNameFromVT(tb_01_Prefix.Text, editedLevelNameForViewAndSheetName, drawingType, ParentName, SelectedParentScale, tb_01_Suffix.Text),
                                    rowView.Cells[1].Value.ToString(),
                                    rowView.Cells[3].Value.ToString(),
                                    rowLevel.Cells[0].Value.ToString(),
                                    rowView.Cells[0].Value.ToString(),
                                    rowScopeBox.Cells[0].Value.ToString(),
                                    SelectedParentScale,
                                    "", // No DisciplineName for parent not placed in sheet
                                    ParentName, // Tag as Parent in Cell[11] = View
                                    VSC_Utilities.ExtractBaseViewNameFromVT(tb_01_Prefix.Text, editedLevelNameForViewAndSheetName, drawingType), // BaseViewName
                                    "", // Designer
                                    "", // DrawnBy
                                    "" // Date
                                    );
                            }

                            // Create Dependent
                            if (!Convert.ToBoolean(rowScopeBox.Cells[3].Value.ToString()) && CreateParent)
                            {
                                var sheetNumber = VSC_Utilities.ExtractSheetNumberFromVT(selectedDiscipline, sequenceNumber, editedLevelNameForSheetNumber, numberInput);

                                _DataTable.Rows.Add(
                                    sheetNumber,
                                    VSC_Utilities.ExtractSheetNameFromVT(tb_01_Prefix.Text, editedLevelNameForViewAndSheetName, drawingType, stringInput, tb_01_Suffix.Text),
                                    VSC_Utilities.ExtractViewNameFromVT(tb_01_Prefix.Text, editedLevelNameForViewAndSheetName, drawingType, stringInput, SelectedParentScale, tb_01_Suffix.Text),
                                    rowView.Cells[1].Value.ToString(),
                                    rowView.Cells[3].Value.ToString(),
                                    rowLevel.Cells[0].Value.ToString(),
                                    rowView.Cells[0].Value.ToString(),
                                    rowScopeBox.Cells[0].Value.ToString(),
                                    SelectedParentScale,
                                    rowView.Cells[4].Value.ToString(), // Cells[4] = DisciplineName
                                    "Dependent", // Tag as Dependent in Cell[11] = View
                                    VSC_Utilities.ExtractBaseViewNameFromVT(tb_01_Prefix.Text, editedLevelNameForViewAndSheetName, drawingType), // BaseViewName
                                    "", // Designer
                                    "", // DrawnBy
                                    "" // Date
                                    );
                            }

                        }
                    }
                }
                // Save ScopeBox and Level inputs to XML
                if (Directory.Exists(VSC_Path))
                {
                    DT_ScopeBox.WriteXml(XML_ScopeBoxFileName, XmlWriteMode.WriteSchema);
                    DT_Level.WriteXml(XML_LevelFileName, XmlWriteMode.WriteSchema);
                }

                // Open Tab 2
                tabControl1.SelectTab(1);
            }
        }

        private void dgv_02_ViewsToBePlaced_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            if (e.ColumnIndex == 0)
            {
                indexRow = e.RowIndex;
                DataGridViewRow row = dgv_02_ViewsToBePlaced.Rows[indexRow];

                var sheetNum = row.Cells[1].Value.ToString();
                var first4Char = sheetNum.Substring(0, sheetNum.Length - 3);

                dgv_02_ViewsToBePlaced.Columns["Sheet Number"].ReadOnly = false;
                row.Cells[1].Value = first4Char + row.Cells[0].Value;
                dgv_02_ViewsToBePlaced.Columns["Sheet Number"].ReadOnly = true;
            }
        }

        private void btn_02_Start_Click(object sender, EventArgs e)
        {
            // Check Beca Titleblock selection
            DialogResult dr = DialogResult.Yes;
            if (!lb_02_TitleBlocks.SelectedItem.ToString().Contains("Beca"))
                dr = MessageBox.Show("Non Beca Titleblock is selected.\nWould you like to proceed?", "Titleblock Selection", MessageBoxButtons.YesNo);
            if (dr == DialogResult.No)
            {
                this.DialogResult = DialogResult.None;
                tabControl1.SelectTab(1);
            }
            if (dr == DialogResult.Yes)
            {
                // Check duplicate sheet number in list
                var sheets = new List<string>();
                var views = new List<string>();
                foreach (DataGridViewRow row in dgv_02_ViewsToBePlaced.Rows)
                {
                    if (row.Cells[1].Value.ToString() != "-")
                        sheets.Add(row.Cells[1].Value.ToString());
                    views.Add(row.Cells[3].Value.ToString());
                }
                var duplicateSheetNumbers = sheets.GroupBy(x => x).Where(g => g.Count() > 1).Select(y => y.Key).ToList();
                var duplicateViewNames = views.GroupBy(x => x).Where(g => g.Count() > 1).Select(y => y.Key).ToList();

                // Check duplicate sheet number in project
                var duplicateSheetNumbersInProject = sheets.Intersect(_Data.SheetNumbers).ToList();
                var duplicateViewNamesInProject = views.Intersect(_Data.ViewNames).ToList();
                var sbSheets = new StringBuilder();
                var sbViews = new StringBuilder();
                if (duplicateSheetNumbersInProject.Count > 0 || duplicateViewNamesInProject.Count > 0)
                {
                    if (duplicateSheetNumbersInProject.Count > 0)
                    {
                        foreach (var d in duplicateSheetNumbersInProject)
                            sbSheets.AppendLine(d);
                        MessageBox.Show("Sheet Numbers already exists in project.\nPlease recheck list:\n" + sbSheets.ToString(), "Duplicates Items");
                        this.DialogResult = DialogResult.None;
                    }
                    if (duplicateViewNamesInProject.Count > 0)
                    {
                        foreach (var d in duplicateViewNamesInProject)
                            sbViews.AppendLine(d);
                        MessageBox.Show("View Names already exists in project.\nPlease recheck list:\n" + sbViews.ToString(), "Duplicates Items");
                        this.DialogResult = DialogResult.None;
                    }

                }
                else
                {
                    if (duplicateSheetNumbers.Any() || duplicateViewNames.Any())
                    {
                        if (duplicateSheetNumbers.Count > 0)
                        {
                            MessageBox.Show("Duplicate Sheet Numbers found.\nPlease recheck list.", "Duplicates Items");
                            this.DialogResult = DialogResult.None;
                        }
                        if (duplicateViewNames.Count > 0)
                        {
                            MessageBox.Show("Duplicate View Names found.\nPlease recheck list.", "Duplicates Items");
                            this.DialogResult = DialogResult.None;
                        }

                    }
                    else // No duplicates > Go!
                    {
                        var titleblockId = _Data.TitleBlocks.Find(x => x.Name == lb_02_TitleBlocks.SelectedItem.ToString()).Id;
                        foreach (DataGridViewRow row in dgv_02_ViewsToBePlaced.Rows)
                        {
                            Element scopeBox = null;
                            if (row.Cells[8].Value.ToString() != "")
                                scopeBox = _Data.ScopeBoxes.Find(x => x.ScopeBoxName == row.Cells[8].Value.ToString()).ScopeBox;

                            if (row.Cells[11].Value.ToString() == OverallName || row.Cells[11].Value.ToString() == ParentName)
                                ViewSheetCreationItems.Add(new VSC_CreationItems(
                                    _Doc,
                                    false,
                                    row,
                                    titleblockId,
                                    _Data.Levels.Find(x => x.LevelName == row.Cells[6].Value.ToString()).Level,
                                    scopeBox
                                ));
                            else
                            {
                                ViewSheetCreationItems.Add(new VSC_CreationItems(
                                    _Doc,
                                    true,
                                    row,
                                    titleblockId,
                                    _Data.Levels.Find(x => x.LevelName == row.Cells[6].Value.ToString()).Level,
                                    scopeBox
                                ));
                            }
                        }
                    }
                }

                WriteInfoXML();
            }

        }

        private void btn_01_RemoveSelectedViews_Click(object sender, EventArgs e)
        {
            foreach (DataGridViewRow row in dgv_02_ViewsToBePlaced.SelectedRows)
            {
                dgv_02_ViewsToBePlaced.Rows.RemoveAt(row.Index);
            }
        }

        private void btn_01_clearAll_Click(object sender, EventArgs e)
        {
            ClearDgv(dgv_02_ViewsToBePlaced);
        }

        private void btn_02_ClearSelected_Click(object sender, EventArgs e)
        {
            foreach (DataGridViewRow row in adgv_01_AvailableVT.SelectedRows)
            {
                row.Cells[2].Value = "";
                row.Cells[3].Value = "";
            }
        }

        private void btn_02_ClearAll_Click(object sender, EventArgs e)
        {
            foreach (DataGridViewRow row in adgv_01_AvailableVT.Rows)
            {
                row.Cells[2].Value = "";
                row.Cells[3].Value = "";
            }
        }

        protected override void btnHelp_Click(object sender, EventArgs e)
        {
            using (var helpFrm = new FrmSheetCreatorHelp())
            {
                helpFrm.ShowDialog();
            }
        }

        private void btn_02_ApplySettings_Click(object sender, EventArgs e)
        {
            // Check Beca Titleblock selection
            DialogResult dr = DialogResult.Yes;
            if (!lb_02_TitleBlocks.SelectedItem.ToString().Contains("Beca"))
                dr = MessageBox.Show("Non Beca Titleblock is selected.\nWould you like to proceed?", "Titleblock Selection", MessageBoxButtons.YesNo);
            if (dr == DialogResult.No)
                this.DialogResult = DialogResult.None;
            if (dr == DialogResult.Yes)
            {
                // Set Designer, DrawnBY, Data to dgv_02_ViewsToBePlaced
                foreach (DataGridViewRow tbRow in dgv_02_TitleblockSetting.Rows)
                {
                    foreach (DataGridViewRow vRow in dgv_02_ViewsToBePlaced.Rows)
                    {
                        if (vRow.Cells[1].Value.ToString() != "-" && vRow.Cells[1].Value.ToString().Substring(0, 2) == tbRow.Cells[0].Value.ToString())
                        {
                            vRow.Cells[13].Value = tbRow.Cells[1].Value.ToString();
                            vRow.Cells[14].Value = tbRow.Cells[2].Value.ToString();
                            vRow.Cells[15].Value = tbRow.Cells[3].Value.ToString();
                        }
                    }
                }

                if (dr == DialogResult.Yes)
                    tabControl1.SelectTab(2);
            }
            // Save Titleblock setting inputs to XML
            DT_TitleblockSetting.WriteXml(XML_TitleblockSettingFileName, XmlWriteMode.WriteSchema);
        }

        private void dgv_02_TitleblockSetting_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            if (e.ColumnIndex == 1 || e.ColumnIndex == 2)
            {
                if (dgv_02_TitleblockSetting.Rows[e.RowIndex].Cells[1].Value.ToString() != "" || dgv_02_TitleblockSetting.Rows[e.RowIndex].Cells[2].Value.ToString() != "")
                    dgv_02_TitleblockSetting.Rows[e.RowIndex].Cells[3].Value = DateTime.Now.ToString("dd'.'MM'.'yyyy");
                else
                    dgv_02_TitleblockSetting.Rows[e.RowIndex].Cells[4].Value = "";
            }
        }

        private void btn_SaveSettings_Click(object sender, EventArgs e)
        {
            // Save ScopeBox and Level inputs to XML
            if (Directory.Exists(VSC_Path))
            {
                DT_ScopeBox.WriteXml(XML_ScopeBoxFileName, XmlWriteMode.WriteSchema);
                DT_Level.WriteXml(XML_LevelFileName, XmlWriteMode.WriteSchema);
            }
            else
            {
                MessageBox.Show("Cannot connect to P: drive.\nSettings will not be saved.");
            }
        }

        private void tb_CustomScaleOverall_TextChanged(object sender, EventArgs e)
        {
            if (tb_CustomScaleOverall.Text != "")
            {
                cb_01_OverallViewScale.Enabled = false;
                SelectedOverallScale = "1-" + tb_CustomScaleOverall.Text;
            }
            else
            {
                cb_01_OverallViewScale.Enabled = true;
                SelectedOverallScale = cb_01_OverallViewScale.SelectedItem.ToString();
            }
                
        }

        private void tb_CustomScaleParent_TextChanged(object sender, EventArgs e)
        {
            if (tb_CustomScaleParent.Text != "")
            {
                cb_01_ParentViewScale.Enabled = false;
                SelectedParentScale = "1-" + tb_CustomScaleParent.Text;
            }
            else
            {
                cb_01_ParentViewScale.Enabled = true;
                SelectedParentScale = cb_01_ParentViewScale.SelectedItem.ToString();
            }
        }
    }
}