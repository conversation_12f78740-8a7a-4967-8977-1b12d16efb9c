﻿using Autodesk.Revit.DB;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MEP.SheetCreator.UI.Forms
{
    public partial class FrmParameterList : System.Windows.Forms.Form
    {
        public string ParametersPrefix { get; set; }

        public FrmParameterList(IList<Parameter> parameters)
        {
            InitializeComponent();
    
            lb_Parameters.DataSource = parameters.Select(x => x.Definition.Name).ToList();
        }

        private void lb_Parameters_DoubleClick(object sender, EventArgs e)
        {
            var ss = lb_Parameters.SelectedItem.ToString();
            ParametersPrefix = $"{rtb_PrefixPreview.Text} _{ss}";
            rtb_PrefixPreview.Text = ParametersPrefix;
        }

        private void btn_RemoveLast_Click(object sender, EventArgs e)
        {
            if (ParametersPrefix == null)
                return;

            var index = ParametersPrefix.LastIndexOf("_");
            if (index >= 0)
                ParametersPrefix = ParametersPrefix.Substring(0, index);
            rtb_PrefixPreview.Text = ParametersPrefix;

        }
    }
}
