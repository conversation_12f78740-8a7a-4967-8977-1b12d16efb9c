﻿using Autodesk.Revit.DB;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MEP.SheetCreator.CoreLogic
{
    public class VSC_CreationItems
    {

        #region Fields
        public string SheetName;
        public string SheetNumber;
        public ElementId TitleBlockId;

        public Autodesk.Revit.DB.View View;
        public string ViewName;
        public string ViewTemplateName;
        public string SecondaryViewTemplateName;

        public ElementId LevelId;
        public ViewFamilyType ViewFamilyType;

        public bool IsDependentView;
        public string DependentViewName;
        public ViewPlan ParentView;
        public Element ScopeBox;

        public int? Scale;

        public string DisciplineName;
        public string OverallParentDependent;
        public string BaseViewName;

        public string Designer;
        public string DrawnBy;
        public string Date;

        #endregion

        #region Properties



        #endregion

        #region Constructors
        public VSC_CreationItems(Document doc, bool isDependent, DataGridViewRow row, ElementId titleBlockId, Level level, Element scopebox)
        {
            SheetName = row.Cells[2].Value.ToString();
            SheetNumber = row.Cells[1].Value.ToString();
            TitleBlockId = titleBlockId;

            if (isDependent)
            {
                IsDependentView = isDependent;
                DependentViewName = row.Cells[3].Value.ToString();
                if (scopebox != null)
                    ScopeBox = scopebox;
            }
            else
            {
                IsDependentView = false;
                ViewName = row.Cells[3].Value.ToString();
                ViewTemplateName = row.Cells[4].Value.ToString();
                SecondaryViewTemplateName = row.Cells[5].Value.ToString();
                if (scopebox != null)
                    ScopeBox = scopebox;
            }
            
            LevelId = level.Id;
            ViewFamilyType = GetViewFamilyType(doc, row.Cells[6].Value.ToString());

            var sc = row.Cells[9].Value.ToString();
            if (sc != "")
                Scale = Convert.ToInt32(sc.Remove(0, 2));

            DisciplineName = row.Cells[10].Value.ToString();
            OverallParentDependent = row.Cells[11].Value.ToString();
            BaseViewName = row.Cells[12].Value.ToString();

            Designer = row.Cells[13].Value.ToString();
            DrawnBy = row.Cells[14].Value.ToString();
            Date = row.Cells[15].Value.ToString();
        }


        #endregion

        #region Methods

        private ViewFamilyType GetViewFamilyType(Document doc, string viewTypeName)
        {
            return new FilteredElementCollector(doc).OfClass(typeof(ViewFamilyType))
                .Cast<ViewFamilyType>().FirstOrDefault<ViewFamilyType>(x => GetViewFamily(viewTypeName) == x.ViewFamily);
        }

        private ViewFamily GetViewFamily(string viewTypeName)
        {
            switch (viewTypeName)
            {
                case "FloorPlan":
                    return ViewFamily.FloorPlan;
                case "ThreeD":
                    return ViewFamily.ThreeDimensional;
                case "Elevation":
                    return ViewFamily.Elevation;
                case "Section":
                    return ViewFamily.Section;
                case "CeilingPlan":
                    return ViewFamily.CeilingPlan;
                case "EngineeringPlan":
                    return ViewFamily.StructuralPlan;
                case "DraftingView":
                    return ViewFamily.Drafting;
                default:
                    return ViewFamily.FloorPlan;
            }
        }

        #endregion

    }
}