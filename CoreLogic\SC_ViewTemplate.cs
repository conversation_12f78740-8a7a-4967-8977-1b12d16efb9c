﻿using Autodesk.Revit.DB;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using View = Autodesk.Revit.DB.View;


namespace MEP.SheetCreator.CoreLogic
{
    public class SC_ViewTemplate
    {

        #region Fields
        public View View;
        public string ViewType { get; set; }
        public string Name { get; set; }
        public string Secondary { get; set; }

        #endregion

        #region Properties



        #endregion

        #region Constructors
        public SC_ViewTemplate(View v)
        {
            View = v;
            Name = v.Name;
            ViewType = v.ViewType.ToString();
            Secondary = "None";
        }
        #endregion

        #region Methods

        #endregion

    }
}