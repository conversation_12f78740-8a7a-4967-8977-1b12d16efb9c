﻿using Autodesk.Revit.ApplicationServices;
using Autodesk.Revit.DB;
using Common.Extensions;
using MEP.SheetCreator.UI.Forms;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Xml.Serialization;

namespace MEP.SheetCreator.CoreLogic.CombinationFormLogic.VSC_2
{
    public class VSC_2
    {
        #region Fields
        FrmSheetAndViewCreator _form;
        Document _Doc;
        public VSC_Data Data;

        int indexRow;

        public List<VSC_CreationItems> ViewSheetCreationItems;
        public Dictionary<ElementId, string> TitleBlocksDict;
        public DataTable DataTable;
        public DataTable DT_ViewTemplate;

        string OverallName;
        string ParentName;

        public bool CreateParent;

        List<string> Scales;

        string SelectedOverallScale;
        string SelectedParentScale;


        // For Serialization
        string VSC_Path;
        string XML_ScopeBoxFileName;
        string XML_LevelFileName;
        string XML_InfoFileName;
        string XML_TitleblockSettingFileName;
        DataTable DT_ScopeBox = new DataTable("ScopeBox");
        DataTable DT_Level = new DataTable("Level");
        DataTable DT_TitleblockSetting = new DataTable("TitleblockSetting");

        string UserName = Environment.UserName;
        string UpdateTime = DateTime.Now.ToString("MM/dd/yyyy hh:mm tt");
        string ComputerName = Environment.MachineName;
        string ProjectName;

        VSC_Info UpdateInfo;

        #endregion
        public VSC_2(Autodesk.Revit.ApplicationServices.Application app, Document doc, FrmSheetAndViewCreator form, DataTable dt)
        {
            #region Data Preparation and Form
            _form = form;
            DataTable = dt;
            var version = app.VersionNumber;
            Data = new VSC_Data(doc);

            if (Data.ScopeBoxes.Count() == 0)
            {
                MessageBox.Show("No Scope Box found.\nPlease create Overall and Zone Scope Boxes.", "Null Scope Box");
            }
            if (Data.ViewTemplates.Count() == 0)
            {
                MessageBox.Show("No View Template found.\nThis tool will not work without View Template.", "Null View Template");
            }

            DT_ViewTemplate = Data.ViewTemplates.ToDataTable();

            PopulateData(doc, form.OverallName, form.ParentName, version, form);
            #endregion
        }

        private void PopulateData(Document doc, string overall, string parent, string version, FrmSheetAndViewCreator form)
        {
            // Path and file name settings for XML Serialization
            ProjectName = doc.Title;

            #region XML Serialization
            //VSC_Path = Path.Combine(new string[] { @"P:\513\5130000\VSC", version, ProjectName });
            //try
            //{
            //    Directory.CreateDirectory(VSC_Path);
            //}
            //catch (Exception)
            //{

            //    MessageBox.Show("Cannot access P drive", "Missing path");
            //}

            //XML_ScopeBoxFileName = Path.Combine(new string[] { VSC_Path, "VSC_ScopeBox.xml" });
            //XML_LevelFileName = Path.Combine(new string[] { VSC_Path, "VSC_Level.xml" });
            //XML_InfoFileName = Path.Combine(new string[] { VSC_Path, "VSC_Info.xml" });
            //XML_TitleblockSettingFileName = Path.Combine(new string[] { VSC_Path, "VSC_Titleblock.xml" });

            //if (File.Exists(XML_InfoFileName))
            //{
            //    UpdateInfo = ReadInfoXML();
            //    form.lbl_ProjectName.Text = UpdateInfo.ProjectName;
            //    form.lbl_LastUpdatedBy.Text = UpdateInfo.LastUpdatedBy;
            //    form.lbl_UpdateTime.Text = UpdateInfo.UpdateTime;
            //}
            //else
            //    MessageBox.Show("Cannot get last update info.\nPlease check connection to database.", "Connection issue");
            #endregion

            _Doc = doc;
            OverallName = overall;
            ParentName = parent;

            PopulateLevelList(Data);
            PopulateScopeBoxList(Data);
            PopulateTitleBlockList(Data);

            //_form.lb_01_2ndVT.SelectedIndex = 0;

            PopulateDrawingTypeList();

            PopulateViewAndDrawingTypeList(Data);
            InitializePreviewList();

            ViewSheetCreationItems = new List<VSC_CreationItems>();

            CreateParent = false;

            Scales = Data.Scales;

            foreach (var s in Scales)
            {
                _form.cb_01_OverallViewScale.Items.Add(s);
                _form.cb_01_ParentViewScale.Items.Add(s);
            }
            _form.cb_01_OverallViewScale.SelectedIndex = 8;
            SelectedOverallScale = _form.cb_01_OverallViewScale.SelectedItem.ToString();
            _form.cb_01_ParentViewScale.SelectedIndex = 7;
            SelectedParentScale = _form.cb_01_ParentViewScale.SelectedItem.ToString();

        }

        private void PopulateDrawingTypeList()
        {
            foreach (var item in Data.Disciplines.OrderBy(x => x.SequenceNumber))
            {
                _form.dgv_01_DrawingTypeList.Rows.Add(false, item.SequenceNumber.ToString(), item.Name, item.DrawingType, item.CustomInput, item.LongName, "", "", "", "");
            }
        }

        private void PopulateLevelList(VSC_Data data)
        {
            foreach (var item in data.Levels)
            {
                _form.dgv_01_Levels.Rows.Add(false, item.LevelName, item.LevelName, item.InputNumber);
            }

            //if (File.Exists(XML_LevelFileName))
            //{
            //    // Load saved Level inputs
            //    var ss = DT_Level.ReadXml(XML_LevelFileName);
            //    _form.dgv_01_Levels.DataSource = DT_Level;
            //    _form.dgv_01_Levels.RowHeadersVisible = false;
            //    _form.dgv_01_Levels.Columns["Level Name"].ReadOnly = true;
            //}
            //else
            //{
            //    DT_Level.Columns.Add("Level Name");
            //    DT_Level.Columns.Add("Input String");
            //    //    DT_Level.Columns.Add("Input Number");

            //    //    foreach (var item in data.Levels)
            //    //    {
            //    //        DT_Level.Rows.Add(item.LevelName, item.InputString, item.InputNumber);
            //    //    }

            //    //    _form.dgv_01_Levels.DataSource = DT_Level;
            //    //    _form.dgv_01_Levels.RowHeadersVisible = false;
            //    //    _form.dgv_01_Levels.Columns["Level Name"].ReadOnly = true;
            //    //}
            //}
        }

        private void PopulateScopeBoxList(VSC_Data data)
        {
            //DT_ScopeBox.Columns.Add("Scope Box Name");
            //DT_ScopeBox.Columns.Add("Input String");
            //DT_ScopeBox.Columns.Add("Input Number");
            //DT_ScopeBox.Columns.Add(OverallName, System.Type.GetType("System.Boolean"));

            foreach (var item in data.ScopeBoxes)
            {
                _form.dgv_01_ScopeBox.Rows.Add(false, item.ScopeBoxName, item.ScopeBoxName, item.InputNumber, false);
            }

            //_form.dgv_01_ScopeBox.DataSource = DT_ScopeBox;
            //_form.dgv_01_ScopeBox.RowHeadersVisible = false;
            //_form.dgv_01_ScopeBox.Columns["Scope Box Name"].ReadOnly = true;
            //if (File.Exists(XML_ScopeBoxFileName))
            //{
            //    // Load saved ScopeBox input
            //    DT_ScopeBox.ReadXml(XML_ScopeBoxFileName);

            //    _form.dgv_01_ScopeBox.DataSource = DT_ScopeBox;
            //    _form.dgv_01_ScopeBox.RowHeadersVisible = false;
            //    _form.dgv_01_ScopeBox.Columns["Scope Box Name"].ReadOnly = true;

            //    int diff = data.ScopeBoxes.Count - _form.dgv_01_ScopeBox.Rows.Count;

            //    for (int i = 0; i < data.ScopeBoxes.Count; i++)
            //    {
            //        if (i < data.ScopeBoxes.Count - diff)
            //            _form.dgv_01_ScopeBox.Rows[i].Cells[0].Value = data.ScopeBoxes[i].ScopeBoxName;
            //        else
            //            DT_ScopeBox.Rows.Add(data.ScopeBoxes[i].ScopeBoxName, "", "");

            //    }
            //}
            //else
            //{
            //    DT_ScopeBox.Columns.Add("Scope Box Name");
            //    DT_ScopeBox.Columns.Add("Input String");
            //    DT_ScopeBox.Columns.Add("Input Number");
            //    DT_ScopeBox.Columns.Add(OverallName, System.Type.GetType("System.Boolean"));

            //    foreach (var item in data.ScopeBoxes)
            //    {
            //        DT_ScopeBox.Rows.Add(item.ScopeBoxName, item.InputName, item.InputNumber, false);
            //    }

            //    _form.dgv_01_ScopeBox.DataSource = DT_ScopeBox;
            //    _form.dgv_01_ScopeBox.RowHeadersVisible = false;
            //    _form.dgv_01_ScopeBox.Columns["Scope Box Name"].ReadOnly = true;
            //}


        }

        private void PopulateTitleBlockList(VSC_Data data)
        {
            _form.lb_AdvTitleBlocks.DataSource = data.TitleBlocks.Select(x => x.Name).ToList();

        }

        private void InitializePreviewList()
        {
            DataTable = new DataTable();
            DataTable.Columns.Add(AdvancedItemsColumn.SheetNumber.ToString());
            DataTable.Columns.Add(AdvancedItemsColumn.SheetName.ToString());
            DataTable.Columns.Add(AdvancedItemsColumn.ViewName.ToString());
            DataTable.Columns.Add(AdvancedItemsColumn.PrimaryViewTemplate.ToString());
            DataTable.Columns.Add(AdvancedItemsColumn.SecondaryViewTemplate.ToString());
            DataTable.Columns.Add(AdvancedItemsColumn.Level.ToString());
            DataTable.Columns.Add(AdvancedItemsColumn.ViewType.ToString());
            DataTable.Columns.Add(AdvancedItemsColumn.ScopeBox.ToString());
            DataTable.Columns.Add(AdvancedItemsColumn.Scale.ToString());
            DataTable.Columns.Add(AdvancedItemsColumn.DisciplineName.ToString());
            DataTable.Columns.Add(AdvancedItemsColumn.View.ToString());
            DataTable.Columns.Add(AdvancedItemsColumn.BaseViewName.ToString());
            DataTable.Columns.Add(AdvancedItemsColumn.Designer.ToString());
            DataTable.Columns.Add(AdvancedItemsColumn.DrawnBy.ToString());
            DataTable.Columns.Add(AdvancedItemsColumn.VerifiedBy.ToString());
            DataTable.Columns.Add(AdvancedItemsColumn.Date.ToString());

            _form.dgv_02_ViewsToBePlaced.DataSource = DataTable;
            _form.dgv_02_ViewsToBePlaced.RowHeadersVisible = false;

            _form.dgv_02_ViewsToBePlaced.Columns[(int)AdvancedItemsColumn.SheetNumber].ReadOnly = true;
            _form.dgv_02_ViewsToBePlaced.Columns[(int)AdvancedItemsColumn.SheetName].ReadOnly = true;
            _form.dgv_02_ViewsToBePlaced.Columns[(int)AdvancedItemsColumn.ViewName].ReadOnly = true;
            _form.dgv_02_ViewsToBePlaced.Columns[(int)AdvancedItemsColumn.PrimaryViewTemplate].ReadOnly = true;
            _form.dgv_02_ViewsToBePlaced.Columns[(int)AdvancedItemsColumn.SecondaryViewTemplate].ReadOnly = true;
            _form.dgv_02_ViewsToBePlaced.Columns[(int)AdvancedItemsColumn.Level].ReadOnly = true;
            _form.dgv_02_ViewsToBePlaced.Columns[(int)AdvancedItemsColumn.ViewType].ReadOnly = true;
            _form.dgv_02_ViewsToBePlaced.Columns[(int)AdvancedItemsColumn.ScopeBox].ReadOnly = true;
            _form.dgv_02_ViewsToBePlaced.Columns[(int)AdvancedItemsColumn.Scale].ReadOnly = true;

            _form.dgv_02_ViewsToBePlaced.Columns[(int)AdvancedItemsColumn.DisciplineName].Visible = false;
            _form.dgv_02_ViewsToBePlaced.Columns[(int)AdvancedItemsColumn.BaseViewName].Visible = false;
        }

        private void PopulateViewAndDrawingTypeList(VSC_Data data)
        {
            var ss = DT_ViewTemplate.Rows[0].ItemArray[0] as VSC_ViewTemplate;
            foreach (var item in data.ViewTemplates.OrderBy(x => x.ViewType))
            {
                _form.adgv_01_AvailableVT.Rows.Add(item.Selected, item.ViewType, item.Name, null, item.Secondary, item.DisciplineName);
            }

        }

        private VSC_Info ReadInfoXML()
        {
            var reader = new XmlSerializer(typeof(VSC_Info));
            var file = new StreamReader(XML_InfoFileName);
            var vsc_Info = (VSC_Info)reader.Deserialize(file);
            file.Close();

            return vsc_Info;
        }

    }
}
