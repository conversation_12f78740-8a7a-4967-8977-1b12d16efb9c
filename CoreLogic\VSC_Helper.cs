﻿using Autodesk.Revit.DB;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Controls;

namespace MEP.SheetCreator.CoreLogic
{
    public static class VSC_Helper
    {
        public static List<RevitLinkType> UnloadLinkedModels(Document doc)
        {
            List<RevitLinkType> unloadedLinks = new List<RevitLinkType>();

            // Get all RevitLinkType elements
            FilteredElementCollector collector = new FilteredElementCollector(doc)
                .OfClass(typeof(RevitLinkType));

            foreach (RevitLinkType linkType in collector.Cast<RevitLinkType>())
            {
                ElementId typeId = linkType.Id;

                // Check if the link is loaded
                if (RevitLinkType.IsLoaded(doc, typeId))
                {
                    linkType.Unload(null); // Unload the link
                    unloadedLinks.Add(linkType);
                }
            }

            return unloadedLinks;
        }

        // Method to reload the previously unloaded links
        public static void ReloadLinkedModels(Document doc, List<RevitLinkType> unloadedLinks)
        {
            foreach (RevitLinkType linkType in unloadedLinks)
            {
                ElementId typeId = linkType.Id;

                // Check if the link is not loaded using the static IsLoaded method
                if (!RevitLinkType.IsLoaded(doc, typeId))
                {
                    linkType.Reload(); // Reload the link
                }
            }
        }

        public static void ApplyViewFilter(Document doc, ViewPlan view, ElementId levelId)
        {
            // Find the specific level by name
            Level specificLevel = new FilteredElementCollector(doc)
                .OfClass(typeof(Level))
                .Cast<Level>()
                .FirstOrDefault(l => l.Id == levelId);

            if (specificLevel == null)
            {
                MessageBox.Show("Specific level not found.");
                return;
            }

            // Create a parameter filter rule to match the specific level
            ParameterValueProvider provider = new ParameterValueProvider(
                new ElementId(BuiltInParameter.INSTANCE_REFERENCE_LEVEL_PARAM)); 
            FilterRule rule = new FilterElementIdRule(provider, new FilterNumericEquals(), specificLevel.Id);
            ElementParameterFilter elementFilter = new ElementParameterFilter(rule);

            // Dynamically get categories visible in the view
            ICollection<ElementId> categoryIds = GetVisibleCategories(view);

            if (categoryIds == null || categoryIds.Count == 0)
            {
                MessageBox.Show("Error", "No visible categories found in the view.");
                return;
            }

            // Create a ParameterFilterElement in the document
            string filterName = "LevelFilter_" + specificLevel.Name;
            ParameterFilterElement paramFilter = ParameterFilterElement.Create(doc, filterName, categoryIds, elementFilter);

            if (paramFilter == null)
            {
                MessageBox.Show("Error", "Failed to create the parameter filter.");
                return;
            }

            // Add and apply the filter to the view
            view.AddFilter(paramFilter.Id);
            view.SetFilterVisibility(paramFilter.Id, false);
        }

        private static ICollection<ElementId> GetVisibleCategories(Autodesk.Revit.DB.View view)
        {
            ICollection<ElementId> visibleCategories = new List<ElementId>();

            // Iterate through all categories and check visibility in the view
            foreach (Category category in view.Document.Settings.Categories)
            {
                if (view.GetCategoryHidden(category.Id) == false) // Only add visible categories
                {
                    visibleCategories.Add(category.Id);
                }
            }

            return visibleCategories;
        }
    }
}
