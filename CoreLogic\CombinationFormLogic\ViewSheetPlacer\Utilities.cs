﻿using Autodesk.Revit.DB;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using View = Autodesk.Revit.DB.View;


namespace MEP.SheetCreator.CoreLogic.CombinationFormLogic.ViewSheetPlacer
{
    class Utilities
    {
#if TargetYear2026
        static ElementId ViewNameParameterId(View view)
        {
            return view.get_Parameter(BuiltInParameter.VIEW_NAME).Id;
        }
        public static FilterRule ViewNameBeginsWith(View view, String text)
        {
            return ParameterFilterRuleFactory.CreateBeginsWithRule(ViewNameParameterId(view), text);
        }

        public static FilterRule ViewNameEndsWith(View view, String text)
        {
            return ParameterFilterRuleFactory.CreateEndsWithRule(ViewNameParameterId(view), text);
        }

        public static FilterRule ViewNameContains(View view, String text)
        {
            return ParameterFilterRuleFactory.CreateContainsRule(ViewNameParameterId(view), text);
        }

        public static FilterRule ViewNameEquals(View view, String text)
        {
            return ParameterFilterRuleFactory.CreateEqualsRule(ViewNameParameterId(view), text);
        }

        public static FilterRule ViewNameNotBeginsWith(View view, String text)
        {
            return ParameterFilterRuleFactory.CreateNotBeginsWithRule(ViewNameParameterId(view), text);
        }

        public static FilterRule ViewNameNotEndsWith(View view, String text)
        {
            return ParameterFilterRuleFactory.CreateNotEndsWithRule(ViewNameParameterId(view), text);
        }

        public static FilterRule ViewNameNotContains(View view, String text)
        {
            return ParameterFilterRuleFactory.CreateNotContainsRule(ViewNameParameterId(view), text);
        }

        public static FilterRule ViewNameNotEquals(View view, String text)
        {
            return ParameterFilterRuleFactory.CreateNotEqualsRule(ViewNameParameterId(view), text);
        }
#else
        static ElementId ViewNameParameterId(View view)
        {
            return view.get_Parameter(BuiltInParameter.VIEW_NAME).Id;
        }
        public static FilterRule ViewNameBeginsWith(View view, String text)
        {
            return ParameterFilterRuleFactory.CreateBeginsWithRule(ViewNameParameterId(view), text, false);
        }

        public static FilterRule ViewNameEndsWith(View view, String text)
        {
            return ParameterFilterRuleFactory.CreateEndsWithRule(ViewNameParameterId(view), text, false);
        }

        public static FilterRule ViewNameContains(View view, String text)
        {
            return ParameterFilterRuleFactory.CreateContainsRule(ViewNameParameterId(view), text, false);
        }

        public static FilterRule ViewNameEquals(View view, String text)
        {
            return ParameterFilterRuleFactory.CreateEqualsRule(ViewNameParameterId(view), text, false);
        }

        public static FilterRule ViewNameNotBeginsWith(View view, String text)
        {
            return ParameterFilterRuleFactory.CreateNotBeginsWithRule(ViewNameParameterId(view), text, false);
        }

        public static FilterRule ViewNameNotEndsWith(View view, String text)
        {
            return ParameterFilterRuleFactory.CreateNotEndsWithRule(ViewNameParameterId(view), text, false);
        }

        public static FilterRule ViewNameNotContains(View view, String text)
        {
            return ParameterFilterRuleFactory.CreateNotContainsRule(ViewNameParameterId(view), text, false);
        }

        public static FilterRule ViewNameNotEquals(View view, String text)
        {
            return ParameterFilterRuleFactory.CreateNotEqualsRule(ViewNameParameterId(view), text, false);
        }
#endif

    }
}
