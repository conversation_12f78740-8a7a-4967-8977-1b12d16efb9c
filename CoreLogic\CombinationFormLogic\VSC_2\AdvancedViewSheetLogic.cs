using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using Common.UI.Forms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TaskDialog = Autodesk.Revit.UI.TaskDialog;
using View = Autodesk.Revit.DB.View;

namespace MEP.SheetCreator.CoreLogic.CombinationFormLogic.VSC_2
{
    public static class AdvancedViewSheetLogic
    {

        public static bool RunLogic(Document doc, List<VSC_2_CreationItems> viewSheetCreationItems, VSC_Data advancedData, VSC_TitleblockParameters titleblockParameters)
        {
            var overallName = "OVERALL";
            var parentName = "PARENT";
            var scaleParameter = "View Scale";
            var secondaryVTParameter = "Beca_SecondaryViewTemplate";

            try
            {
                // List views to place in sheet center
                List<VSC_CenterView> centerView = new List<VSC_CenterView>();

                if (viewSheetCreationItems.Count == 0)
                    return false;

                var overallOrParents = viewSheetCreationItems.Where(x => x.IsDependentView == false).ToList();

                using (var trans = new Transaction(doc, "Create Views and Sheets"))
                {
                    trans.Start();

                    int overallViewCount = 0;
                    int parentViewCount = 0;
                    int dependentViewCount = 0;
                    int sheetCount = 0;

                    var dependents = viewSheetCreationItems.Where(x => x.IsDependentView == true).ToList();
                    string progressMessage = "{0} of " + overallOrParents.Count.ToString() + " views processed...";
                    using (BecaProgressForm pf = new BecaProgressForm("Creating and Placing Overall/Parent items", progressMessage, overallOrParents.Count))
                    {
                        // Create OVERALL Views
                        foreach (var item in overallOrParents)
                        {
                            // Primary and Secondary View Template set up
                            var primaryVT = advancedData.ViewTemplates.Find(x => x.Name == item.ViewTemplateName).View;
                            View secondaryVT = null;
                            if (item.SecondaryViewTemplateName != "")
                            {
                                secondaryVT = advancedData.ViewTemplates.Find(x => x.Name == item.SecondaryViewTemplateName).View;
                                VSC_Utilities.NotIncludeVTParameter(secondaryVT, scaleParameter, secondaryVTParameter); // Uncheck Include View Scale & Beca_SecondaryViewTemplate parameter
                            }

                            // Create PARENT or OVERALL View
                            var view = VSC_Utilities.CreateView(doc, item.ViewFamilyType.Id, item.LevelId, item.ViewName);

                            // Apply View Filter
                            //VSC_Helper.ApplyViewFilter(doc, view, item.LevelId);

                            // Set View Templates
                            view.get_Parameter(BuiltInParameter.VIEW_TEMPLATE).Set(primaryVT.Id);

                            // Set the scope box for the view
                            var id = item.ScopeBox.Id;
                            var currentScopeBoxId = view.get_Parameter(BuiltInParameter.VIEWER_VOLUME_OF_INTEREST_CROP).AsElementId();

                            // Set Secondary VT, Scope Box and Scale
                            if (secondaryVT != null)
                                view.ApplyViewTemplateParameters(secondaryVT);
                            if (view.LookupParameter(secondaryVTParameter) != null)
                                view.LookupParameter(secondaryVTParameter).Set(item.SecondaryViewTemplateName);
                            if (currentScopeBoxId == ElementId.InvalidElementId || !currentScopeBoxId.Equals(id))
                                //view.get_Parameter(BuiltInParameter.VIEWER_VOLUME_OF_INTEREST_CROP).Set(item.ScopeBox.Id);
                            if (item.Scale != null)
                                view.Scale = (int)item.Scale;

                            if (item.OverallParentDependent == overallName)
                                overallViewCount++;

                            // Create PARENT View and assign ParentView property to ViewSheetCreationItems where IsDependent is true
                            if (item.OverallParentDependent == parentName)
                            {
                                viewSheetCreationItems.Where(x => x.OverallParentDependent == "Dependent" && x.BaseViewName == item.BaseViewName).ToList().ForEach(y => y.ParentView = view);
                                parentViewCount++;
                            }


                            if (item.SheetNumber != "-")
                            {
                                // Create Sheet
                                var sheet = VSC_Utilities.CreateSheet(doc, item.TitleBlockId, item.SheetName, item.SheetNumber);
                                // Set Sheet Parameters
                                VSC_Utilities.VSC_2_SetBecaSheetParameters(sheet, item);

                                // Place View
                                var titleblock = doc.GetElement(item.TitleBlockId);
                                var bb = titleblock.get_BoundingBox(sheet);
                                var titleblockCenter = bb.Min.Add(bb.Max).Multiply(0.5);
                                var vp = Viewport.Create(doc, sheet.Id, view.Id, new XYZ(0, 0, 0));
                                centerView.Add(new VSC_CenterView { View = vp, Sheet = sheet, Position = titleblockCenter });

                                sheetCount++;
                            }

                            pf.Increment();
                        }
                    }
                    using (BecaProgressForm pf = new BecaProgressForm("Creating and Placing Dependent items", progressMessage, dependents.Count))
                    {
                        // Create DEPENDENT Views
                        foreach (var item in dependents)
                        {
                            if (item.ParentView == null)
                                continue;

                            // Create dependent view
                            var dependentViewId = item.ParentView.Duplicate(ViewDuplicateOption.AsDependent);
                            var dependentView = doc.GetElement(dependentViewId) as ViewPlan;
                            dependentView.Name = item.DependentViewName;
                            if (item.ScopeBox != null)
                                dependentView.get_Parameter(BuiltInParameter.VIEWER_VOLUME_OF_INTEREST_CROP).Set(item.ScopeBox.Id);

                            dependentViewCount++;

                            // Create Sheet
                            var sheet = VSC_Utilities.CreateSheet(doc, item.TitleBlockId, item.SheetName, item.SheetNumber);
                            // Set Sheet Parameters
                            VSC_Utilities.VSC_2_SetBecaSheetParameters(sheet, item);
                            //Place View
                            var titleblock = doc.GetElement(item.TitleBlockId);
                            var bb = titleblock.get_BoundingBox(sheet);
                            var titleblockCenter = bb.Min.Add(bb.Max).Multiply(0.5);
                            var vp = Viewport.Create(doc, sheet.Id, dependentView.Id, new XYZ(0, 0, 0));
                            centerView.Add(new VSC_CenterView { View = vp, Sheet = sheet, Position = titleblockCenter });

                            sheetCount++;

                            pf.Increment();
                        }

                    }
                    TaskDialog.Show("View Sheet Creation", "Successfully creates:\n" + overallViewCount + " Overall views\n" + parentViewCount + " Parent views\n" + dependentViewCount + " Dependent views\n" + sheetCount + " sheets");

                    trans.Commit();

                }

                using (var trans = new Transaction(doc, "Center View in Sheets"))
                {
                    trans.Start();
                    foreach (var item in centerView)
                    {
                        item.View.SetBoxCenter(item.Position);
                        VSC_Utilities.SetBecaTitleblockParameters(doc, item.Sheet);
                    }
                    trans.Commit();
                }

                using (var trans = new Transaction(doc, "Set titleblock parameters"))
                {
                    trans.Start();
                    foreach (var item in overallOrParents)
                    {
                        SetBecaTitleblockParameter(doc.GetElement(item.TitleBlockId) as FamilyInstance, titleblockParameters);
                    }
                    trans.Commit();
                }

                return true;
            }
            catch (Exception e)
            {
                TaskDialog.Show("Advanced VSC error", e.Message);
                return false;
            }
        }

        public static bool RunLogic2(Document doc, List<VSC_2_CreationItems> viewSheetCreationItems, VSC_Data advancedData, VSC_TitleblockParameters titleblockParameters)
        {
            var overallName = "OVERALL";
            var parentName = "PARENT";
            var scaleParameter = "View Scale";
            var secondaryVTParameter = "Beca_SecondaryViewTemplate";

            try
            {
                // Early exit if no items to process
                if (viewSheetCreationItems.Count == 0)
                    return false;

                // Pre-cache reusable data
                var viewTemplateDict = advancedData.ViewTemplates.ToDictionary(x => x.Name, x => x.View); // Optimized lookup
                var overallOrParents = viewSheetCreationItems.Where(x => !x.IsDependentView).ToList();
                var dependents = viewSheetCreationItems.Where(x => x.IsDependentView).ToList();

                using (var trans = new Transaction(doc, "Create Views and Sheets"))
                {
                    trans.Start();

                    int overallViewCount = 0, parentViewCount = 0, dependentViewCount = 0, sheetCount = 0;

                    // Reduce UI updates: Process Overall/Parent items in batches
                    using (BecaProgressForm pf = new BecaProgressForm("Creating and Placing Overall/Parent items", "{0} of " + overallOrParents.Count + " views processed...", overallOrParents.Count))
                    {
                        foreach (var item in overallOrParents)
                        {
                            if (!viewTemplateDict.TryGetValue(item.ViewTemplateName, out var primaryVT)) // Optimized view template lookup
                                continue;

                            View secondaryVT = null;
                            if (!string.IsNullOrEmpty(item.SecondaryViewTemplateName))
                            {
                                viewTemplateDict.TryGetValue(item.SecondaryViewTemplateName, out secondaryVT);
                                if (secondaryVT != null)
                                    VSC_Utilities.NotIncludeVTParameter(secondaryVT, scaleParameter, secondaryVTParameter);
                            }

                            var view = VSC_Utilities.CreateView(doc, item.ViewFamilyType.Id, item.LevelId, item.ViewName);
                            view.get_Parameter(BuiltInParameter.VIEW_TEMPLATE).Set(primaryVT.Id);

                            if (secondaryVT != null)
                                view.ApplyViewTemplateParameters(secondaryVT);
                            if (view.LookupParameter(secondaryVTParameter) != null)
                                view.LookupParameter(secondaryVTParameter).Set(item.SecondaryViewTemplateName);
                            if (item.ScopeBox != null)
                                view.get_Parameter(BuiltInParameter.VIEWER_VOLUME_OF_INTEREST_CROP).Set(item.ScopeBox.Id);
                            if (item.Scale != null)
                                view.Scale = (int)item.Scale;

                            if (item.OverallParentDependent == overallName)
                                overallViewCount++;

                            if (item.OverallParentDependent == parentName)
                            {
                                viewSheetCreationItems.Where(x => x.OverallParentDependent == "Dependent" && x.BaseViewName == item.BaseViewName)
                                                      .ToList()
                                                      .ForEach(y => y.ParentView = view);
                                parentViewCount++;
                            }

                            if (item.SheetNumber != "-")
                            {
                                var sheet = VSC_Utilities.CreateSheet(doc, item.TitleBlockId, item.SheetName, item.SheetNumber);
                                VSC_Utilities.VSC_2_SetBecaSheetParameters(sheet, item);

                                var titleblock = doc.GetElement(item.TitleBlockId) as FamilyInstance;
                                var bb = titleblock?.get_BoundingBox(sheet); // Ensure bounding box exists
                                if (bb != null)
                                {
                                    var titleblockCenter = bb.Min.Add(bb.Max).Multiply(0.5);
                                    var vp = Viewport.Create(doc, sheet.Id, view.Id, new XYZ(0, 0, 0));
                                    sheetCount++;
                                }
                            }

                            //if (pf.ShouldUpdate()) // Only update the progress form periodically
                                pf.Increment();
                        }
                    }

                    // Process dependent views
                    using (BecaProgressForm pf = new BecaProgressForm("Creating and Placing Dependent items", "{0} of " + dependents.Count + " views processed...", dependents.Count))
                    {
                        foreach (var item in dependents)
                        {
                            if (item.ParentView == null)
                                continue;

                            var dependentViewId = item.ParentView.Duplicate(ViewDuplicateOption.AsDependent);
                            var dependentView = doc.GetElement(dependentViewId) as ViewPlan;
                            dependentView.Name = item.DependentViewName;
                            if (item.ScopeBox != null)
                                dependentView.get_Parameter(BuiltInParameter.VIEWER_VOLUME_OF_INTEREST_CROP).Set(item.ScopeBox.Id);

                            dependentViewCount++;

                            var sheet = VSC_Utilities.CreateSheet(doc, item.TitleBlockId, item.SheetName, item.SheetNumber);
                            VSC_Utilities.VSC_2_SetBecaSheetParameters(sheet, item);

                            var titleblock = doc.GetElement(item.TitleBlockId) as FamilyInstance;
                            var bb = titleblock?.get_BoundingBox(sheet);
                            if (bb != null)
                            {
                                var titleblockCenter = bb.Min.Add(bb.Max).Multiply(0.5);
                                var vp = Viewport.Create(doc, sheet.Id, dependentView.Id, new XYZ(0, 0, 0));
                                sheetCount++;
                            }

                            //if (pf.ShouldUpdate()) // Reduce UI updates
                                pf.Increment();
                        }
                    }

                    trans.Commit();

                    TaskDialog.Show("View Sheet Creation", $"Successfully creates:\n{overallViewCount} Overall views\n{parentViewCount} Parent views\n{dependentViewCount} Dependent views\n{sheetCount} sheets");
                }

                return true;
            }
            catch (Exception e)
            {
                TaskDialog.Show("Advanced VSC error", e.Message + "\n" + e.StackTrace); // Add stack trace for better debugging
                return false;
            }
        }

        private static void SetBecaTitleblockParameter(FamilyInstance titleblock, VSC_TitleblockParameters titleblockParameters)
        {
            var sb = new StringBuilder();
            if (titleblock == null)
                return;

            foreach (Parameter param in titleblock.Parameters)
            {
                sb.AppendLine(param.Definition.Name);
                var definition = param.Definition;
                if (!param.IsReadOnly && definition.Name.ToLower().Contains("original in colour"))
                {
                    param.Set(titleblockParameters.OriginalInColour);
                }
                else if (!param.IsReadOnly && param.Definition.Name.ToLower().Contains("interim issue visibility"))
                {
                    param.Set(titleblockParameters.InterimIssue);
                }
                else if (!param.IsReadOnly && param.Definition.Name.ToLower().Contains("check print"))
                {
                    param.Set(titleblockParameters.BecaCheckPrintVisibility);
                }
            }
            TaskDialog.Show("Info", sb.ToString());

            //sheet.LookupParameter("Original in Colour")?.Set(titleblockParameters.OriginalInColour);
            //sheet.LookupParameter("Interim Issue")?.Set(titleblockParameters.InterimIssue);
            //sheet.LookupParameter("Beca Check Print Visibility")?.Set(titleblockParameters.BecaCheckPrintVisibility);
        }
    }
}
