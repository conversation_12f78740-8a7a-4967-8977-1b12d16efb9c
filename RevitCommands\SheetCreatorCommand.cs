﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using System;
using System.Collections.Generic;
using System.Linq;
using BecaCommand;
using BecaRevitUtilities;
using MEP.SheetCreator.UI.Forms;
using BecaRevitUtilities.Collectors;
using MEP.SheetCreator.CoreLogic;

namespace MEP.SheetCreator.RevitCommands
{
    [Autodesk.Revit.Attributes.Transaction(Autodesk.Revit.Attributes.TransactionMode.Manual)]
    class SheetCreatorCommand : BecaBaseCommand
    {
        public override Result ExecuteBecaCommand(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            UIDocument uidoc = commandData.Application.ActiveUIDocument;
            Document doc = uidoc.Document;

            _taskLogger.PreTaskStart();

            var data = new SC_Data(doc);

            //using (var frmSC = new FrmSheetAndViewCreator(doc, data))
            //{
            //    frmSC.ShowDialog();
            //}

            _taskLogger.PostTaskEnd("Sheet Creator completed.");

            return Result.Succeeded;
        }

        public override string GetAddinAuthor()
        {
           return "Firza Utama";
        }

        public override string GetAddinName()
        {
            return AddinNames.SheetCreator.Value;
        }

        public override string GetCommandSubName()
        {
            return "Sheet Creator";
        }
    }
}
