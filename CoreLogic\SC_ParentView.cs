﻿using Autodesk.Revit.DB;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.SheetCreator.CoreLogic
{
    public class SC_ParentView
    {

        #region Fields

        public ViewPlan ParentView;
        public ViewType ViewType { get; set; }
        public string Name { get; set; }

        #endregion

        #region Properties



        #endregion

        #region Constructors
        public SC_ParentView(ViewPlan vp)
        {
            ParentView = vp;
            ViewType = vp.ViewType;
            Name = vp.Name;
        }


        #endregion

        #region Methods



        #endregion

    }
}
