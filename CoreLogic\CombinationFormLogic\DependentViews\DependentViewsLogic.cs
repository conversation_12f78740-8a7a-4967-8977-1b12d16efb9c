﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using BecaTransactionsNamesManager;
using Common.UI.Forms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.SheetCreator.CoreLogic.CombinationFormLogic.DependentViews
{
    public static class DependentViewsLogic
    {
        public static bool RunLogic(Document doc, IEnumerable<ViewPlan> subViewPlansSel, List<Element> subSBxSel, out int intDepViewsCreated, out List<ElementId> createdDependentViewIds)
        {
            try
            {
                createdDependentViewIds = new List<ElementId>();
                bool blDoCreateDp = true;
                string strDepViewName = "";
                using (var trans = new Transaction(doc, BecaTransactionsNames.DependentView_DuplicateDependantViews.GetHumanReadableString()))
                {
                    trans.Start();
                    intDepViewsCreated = 0;

                    //Start Creating Views by Level, View Type, View Template, and Scope Box
                    int nCount = (subViewPlansSel.Count() * subSBxSel.Count);
                    string progressMessage = "{0} of " + nCount.ToString() + " views processed...";
                    string caption = "Creating Views";
                    using (BecaProgressForm pf = new BecaProgressForm(caption, progressMessage, nCount))
                    {
                        //going through each selected viewplan
                        foreach (ViewPlan rqPviewElem in subViewPlansSel)
                        {
                            //Going through each scope box
                            foreach (var rqSBelem in subSBxSel)
                            {
                                //Decide Dependant view name
                                strDepViewName = rqPviewElem.Name + "_" + rqSBelem.Name;

                                //Check if the view has aldready dependant views
                                ICollection<ElementId> rqPviewCurrentDPs = rqPviewElem.GetDependentViewIds();

                                //If any dependant views found
                                if (rqPviewCurrentDPs.Count != 0)
                                {
                                    //God through each dependant view
                                    foreach (ElementId eid in rqPviewCurrentDPs)
                                    {
                                        ViewPlan rqExistDpView = doc.GetElement(eid) as ViewPlan;

                                        //If the dependant view name is the same as what we want to create
                                        if (strDepViewName == rqExistDpView.Name)
                                        {
                                            //Flag not to create depenant view
                                            blDoCreateDp = false;
                                            break;
                                        }
                                    }
                                }

                                //If the flag is not "false"
                                if (blDoCreateDp)
                                {
                                    //Create the dependant view
                                    ElementId rqPviewElemDupId = rqPviewElem.Duplicate(ViewDuplicateOption.AsDependent);
                                    Element rqPviewDupElem = doc.GetElement(rqPviewElemDupId);
                                    ViewPlan rqPviewDup = rqPviewDupElem as ViewPlan;

                                    //Set the name of the dependant view
                                    rqPviewDup.Name = strDepViewName;
                                    //Set the scope box of the depedant view
                                    rqPviewDup.get_Parameter(BuiltInParameter.VIEWER_VOLUME_OF_INTEREST_CROP).Set(rqSBelem.Id);
                                    createdDependentViewIds.Add(rqPviewDup.Id);
                                    intDepViewsCreated++;
                                }

                                //Reset the flag for the next scope box
                                blDoCreateDp = true;

                                pf.Increment();
                            }
                        }
                    }

                    trans.Commit();
                }
                return true;
            }
            catch (Exception ex)
            {
                intDepViewsCreated = 0;
                createdDependentViewIds = null;
                return false;
            }
            
        }
    }
}
