﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MEP.SheetCreator.UI.UIData
{
    internal static class VSC_AdgvColumnsBinding
    {
        public static void ViewTemplateData(Zuby.ADGV.AdvancedDataGridView adgv_01_AvailableVT, BindingSource bs_AvailableVT)
        {
            adgv_01_AvailableVT.DataSource = bs_AvailableVT;

            adgv_01_AvailableVT.Columns[0].Name = "ViewType";
            adgv_01_AvailableVT.Columns["ViewType"].HeaderText = "View Type\n";
            adgv_01_AvailableVT.Columns[1].Name = "Name";
            adgv_01_AvailableVT.Columns["Name"].HeaderText = "Name\n";
            adgv_01_AvailableVT.Columns[2].Name = "DrawingType";
            adgv_01_AvailableVT.Columns["DrawingType"].HeaderText = "Drawing Type\n";
            adgv_01_AvailableVT.Columns[3].Name = "Secondary";
            adgv_01_AvailableVT.Columns["Secondary"].HeaderText = "Secondary\n";
            adgv_01_AvailableVT.Columns[4].Name = "DisciplineName";
            adgv_01_AvailableVT.Columns["DisciplineName"].HeaderText = "DisciplineName\n";

            adgv_01_AvailableVT.Columns["DisciplineName"].Visible = false;

        }
    }
}
