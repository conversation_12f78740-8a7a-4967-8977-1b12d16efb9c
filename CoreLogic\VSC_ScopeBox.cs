﻿using Autodesk.Revit.DB;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.SheetCreator.CoreLogic
{
    public class VSC_ScopeBox
    {

        #region Fields
        public Element ScopeBox;
        public double Area;
        public string ScopeBoxName { get; set; }
        public string InputName { get; set; }
        public string InputNumber { get; set; }

        #endregion

        #region Properties



        #endregion

        #region Constructors
        public VSC_ScopeBox(Element e)
        {
            ScopeBox = e;
            Area = GetArea(ScopeBox);
            ScopeBoxName = e.Name;
            InputName = "";
            InputNumber = "";
        }


        #endregion

        #region Methods
        private double GetArea(Element sb)
        {
            return (sb.get_BoundingBox(null).Max.X - sb.get_BoundingBox(null).Min.X) * (sb.get_BoundingBox(null).Max.Y - sb.get_BoundingBox(null).Min.Y);
        }


        #endregion

    }
}