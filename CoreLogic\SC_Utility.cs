﻿using Autodesk.Revit.DB;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using BecaRevitUtilities.Collectors;

namespace MEP.SheetCreator.CoreLogic
{
    class SC_Utility
    {
        private void CreateSheetView(Document doc, View3D view3D, FamilySymbol titleBlock)
        {
            if (titleBlock != null)
            {
                using (Transaction t = new Transaction(doc, "Create a new ViewSheet"))
                {
                    t.Start();
                    try
                    {
                        // Create a sheet view
                        var viewSheet = ViewSheet.Create(doc, titleBlock.Id);
                        if (null == viewSheet)
                            throw new Exception("Failed to create new ViewSheet.");

                        // Add passed in view onto the center of the sheet
                        UV location = new UV((viewSheet.Outline.Max.U - viewSheet.Outline.Min.U) / 2,
                                             (viewSheet.Outline.Max.V - viewSheet.Outline.Min.V) / 2);

                        //viewSheet.AddView(view3D, location);
                        VSC_Utilities.CreateOptimizedViewport(doc, viewSheet.Id, view3D.Id, new XYZ(location.U, location.V, 0));

                        t.Commit();
                    }
                    catch
                    {
                        t.RollBack();
                    }
                }
            }
        }

    }
}
