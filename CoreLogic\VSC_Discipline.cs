﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.SheetCreator.CoreLogic
{
    public class VSC_Discipline
    {

        #region Fields
        public string Name { get; set; }
        public string DrawingType { get; set; }
        public int SequenceNumber { get; set; }
        public string LongName { get; set; }
        public string CustomInput { get; set; }



        #endregion

        #region Properties



        #endregion

        #region Constructors
        public VSC_Discipline(string name, string drawingType, int sequenceNumber, string longName)
        {
            Name = name;
            DrawingType = drawingType;
            SequenceNumber = sequenceNumber;
            LongName = longName;
            CustomInput = "";
        }


        #endregion

        #region Methods



        #endregion

    }
}