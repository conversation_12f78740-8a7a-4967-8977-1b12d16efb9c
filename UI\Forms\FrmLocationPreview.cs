﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Windows.Forms.Integration;
using PreviewControl = Autodesk.Revit.UI.PreviewControl;

namespace MEP.SheetCreator.UI.Forms
{
    public partial class FrmLocationPreview : System.Windows.Forms.Form
    {
        ElementHost _elementHost;
        PreviewControl _previewControl;

        public FrmLocationPreview(Document doc, ElementId sheetId)
        {
            InitializeComponent();

            _elementHost = new ElementHost();
            _elementHost.Dock = DockStyle.Fill;
            pnl_LocationPreview.Controls.Add(_elementHost);
            _previewControl?.Dispose();
            _previewControl = new PreviewControl(doc, sheetId);
            _elementHost.Child = _previewControl;
        }
    }
}
